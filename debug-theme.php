<?php
/**
 * Debug script to check theme status
 */

// Include WordPress
require_once('wp-config.php');
require_once('wp-load.php');

echo "=== THEME DEBUG INFO ===\n\n";

// Check current theme
$current_theme = wp_get_theme();
echo "Current Theme: " . $current_theme->get('Name') . "\n";
echo "Theme Version: " . $current_theme->get('Version') . "\n";
echo "Theme Directory: " . $current_theme->get_stylesheet_directory() . "\n";
echo "Theme URL: " . $current_theme->get_stylesheet_directory_uri() . "\n\n";

// Check if our theme files exist
$theme_dir = get_template_directory();
$files_to_check = [
    'style.css',
    'index.php',
    'header.php',
    'footer.php',
    'front-page.php',
    'functions.php',
    'js/theme.js'
];

echo "=== THEME FILES ===\n";
foreach ($files_to_check as $file) {
    $file_path = $theme_dir . '/' . $file;
    if (file_exists($file_path)) {
        echo "✅ $file exists\n";
    } else {
        echo "❌ $file missing\n";
    }
}

echo "\n=== FRONT PAGE SETTINGS ===\n";
echo "Show on front: " . get_option('show_on_front') . "\n";
echo "Page on front: " . get_option('page_on_front') . "\n";

// Check if front page exists
$front_page_id = get_option('page_on_front');
if ($front_page_id) {
    $front_page = get_post($front_page_id);
    if ($front_page) {
        echo "Front page title: " . $front_page->post_title . "\n";
        echo "Front page status: " . $front_page->post_status . "\n";
    }
}

echo "\n=== TEMPLATE HIERARCHY ===\n";
// Check what template would be used
if (is_front_page()) {
    echo "Using front-page.php template\n";
} else {
    echo "Using index.php template\n";
}

echo "\n=== ENQUEUED STYLES ===\n";
global $wp_styles;
if (isset($wp_styles->registered['ezpools-style'])) {
    echo "✅ ezpools-style is registered\n";
    echo "Source: " . $wp_styles->registered['ezpools-style']->src . "\n";
    echo "Version: " . $wp_styles->registered['ezpools-style']->ver . "\n";
} else {
    echo "❌ ezpools-style not registered\n";
}

echo "\n🔄 Try refreshing the page with Ctrl+F5 or Cmd+Shift+R\n";
?>
