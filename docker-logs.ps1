# EZ Pools WordPress Docker Logs Script for Windows
# View logs from the WordPress containers

param(
    [string]$Service = "",
    [switch]$Follow = $false
)

Write-Host "📋 EZ Pools WordPress Docker Logs" -ForegroundColor Cyan
Write-Host "==================================" -ForegroundColor Cyan

if ($Service -eq "") {
    Write-Host ""
    Write-Host "Available services:" -ForegroundColor Yellow
    Write-Host "  - wordpress" -ForegroundColor White
    Write-Host "  - db" -ForegroundColor White
    Write-Host "  - phpmyadmin" -ForegroundColor White
    Write-Host ""
    Write-Host "Usage examples:" -ForegroundColor Cyan
    Write-Host "  .\docker-logs.ps1                    # View all logs" -ForegroundColor White
    Write-Host "  .\docker-logs.ps1 -Service wordpress # View WordPress logs" -ForegroundColor White
    Write-Host "  .\docker-logs.ps1 -Follow            # Follow all logs" -ForegroundColor White
    Write-Host "  .\docker-logs.ps1 -Service db -Follow # Follow database logs" -ForegroundColor White
    Write-Host ""
}

try {
    if ($Follow) {
        if ($Service -eq "") {
            Write-Host "📡 Following logs for all services (Ctrl+C to stop)..." -ForegroundColor Green
            docker-compose logs -f
        } else {
            Write-Host "📡 Following logs for $Service (Ctrl+C to stop)..." -ForegroundColor Green
            docker-compose logs -f $Service
        }
    } else {
        if ($Service -eq "") {
            Write-Host "📄 Showing recent logs for all services..." -ForegroundColor Green
            docker-compose logs --tail=50
        } else {
            Write-Host "📄 Showing recent logs for $Service..." -ForegroundColor Green
            docker-compose logs --tail=50 $Service
        }
    }
} catch {
    Write-Host "❌ Error viewing logs: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "   Make sure the containers are running with: .\docker-start.ps1" -ForegroundColor Yellow
}
