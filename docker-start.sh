#!/bin/bash

# EZ Pools WordPress Docker Startup Script
# This script provides an easy way to start the WordPress environment

set -e

echo "🏊‍♂️ EZ Pools WordPress Docker Setup"
echo "======================================"

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    echo "❌ Docker is not installed. Please install Docker first."
    echo "   Visit: https://docs.docker.com/get-docker/"
    exit 1
fi

# Check if Docker Compose is installed
if ! command -v docker-compose &> /dev/null; then
    echo "❌ Docker Compose is not installed. Please install Docker Compose first."
    echo "   Visit: https://docs.docker.com/compose/install/"
    exit 1
fi

# Check if Docker is running
if ! docker info &> /dev/null; then
    echo "❌ Docker is not running. Please start Docker first."
    exit 1
fi

echo "✅ Docker is ready!"
echo ""

# Check if containers are already running
if docker-compose ps | grep -q "Up"; then
    echo "🔄 WordPress containers are already running."
    echo ""
    echo "📍 Access your site at:"
    echo "   - Website: http://localhost:8080"
    echo "   - WordPress Admin: http://localhost:8080/wp-admin"
    echo "   - Username: admin"
    echo "   - Password: admin123"
    echo "   - phpMyAdmin: http://localhost:8081"
    echo ""
    read -p "Do you want to restart the containers? (y/N): " restart
    if [[ $restart =~ ^[Yy]$ ]]; then
        echo "🔄 Restarting containers..."
        docker-compose down
    else
        echo "✅ Containers are already running. Nothing to do!"
        exit 0
    fi
fi

echo "🚀 Starting EZ Pools WordPress environment..."
echo ""

# Start the containers
docker-compose up -d

echo ""
echo "⏳ Waiting for services to start up..."

# Wait for WordPress to be ready
timeout=60
counter=0
while ! curl -s http://localhost:8080 > /dev/null 2>&1; do
    if [ $counter -ge $timeout ]; then
        echo "❌ Timeout waiting for WordPress to start"
        echo "   Check logs with: docker-compose logs"
        exit 1
    fi
    echo -n "."
    sleep 2
    counter=$((counter + 2))
done

echo ""
echo ""
echo "🎉 EZ Pools WordPress is ready!"
echo "================================"
echo ""
echo "📍 Access your site at:"
echo "   🌐 Website: http://localhost:8080"
echo "   ⚙️  WordPress Admin: http://localhost:8080/wp-admin"
echo "   👤 Username: admin"
echo "   🔑 Password: admin123"
echo "   🗄️  phpMyAdmin: http://localhost:8081"
echo ""
echo "📋 Useful commands:"
echo "   - View logs: docker-compose logs -f"
echo "   - Stop site: docker-compose down"
echo "   - Restart: docker-compose restart"
echo ""
echo "📚 For more commands, see the Makefile or run 'make help'"
echo ""

# Try to open the website in the default browser
if command -v open &> /dev/null; then
    # macOS
    open http://localhost:8080
elif command -v xdg-open &> /dev/null; then
    # Linux
    xdg-open http://localhost:8080
elif command -v start &> /dev/null; then
    # Windows
    start http://localhost:8080
else
    echo "💡 Open http://localhost:8080 in your browser to view the site"
fi

echo "✅ Setup complete! Enjoy your EZ Pools website!"
