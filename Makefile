# EZ Pools WordPress Docker Setup
# Simple commands to manage the Docker environment

.PHONY: help build up down restart logs clean install theme-setup

# Default target
help:
	@echo "EZ Pools WordPress Docker Commands:"
	@echo ""
	@echo "  make up          - Start the WordPress site (builds if needed)"
	@echo "  make down        - Stop the WordPress site"
	@echo "  make restart     - Restart the WordPress site"
	@echo "  make build       - Build the Docker images"
	@echo "  make logs        - View logs from all containers"
	@echo "  make clean       - Remove all containers and volumes (DESTRUCTIVE)"
	@echo "  make install     - Fresh install (stops, cleans, and starts)"
	@echo "  make theme-setup - Run the EZ Pools theme setup"
	@echo ""
	@echo "After running 'make up', visit:"
	@echo "  - Website: http://localhost:8080"
	@echo "  - Admin: http://localhost:8080/wp-admin (admin/admin123)"
	@echo "  - phpMyAdmin: http://localhost:8081"

# Build Docker images
build:
	@echo "Building Docker images..."
	docker-compose build

# Start the WordPress site
up:
	@echo "Starting EZ Pools WordPress site..."
	docker-compose up -d
	@echo ""
	@echo "🎉 EZ Pools WordPress is starting up!"
	@echo ""
	@echo "Please wait a moment for the setup to complete, then visit:"
	@echo "  - Website: http://localhost:8080"
	@echo "  - WordPress Admin: http://localhost:8080/wp-admin"
	@echo "  - Username: admin"
	@echo "  - Password: admin123"
	@echo "  - phpMyAdmin: http://localhost:8081"
	@echo ""
	@echo "To view logs: make logs"

# Stop the WordPress site
down:
	@echo "Stopping EZ Pools WordPress site..."
	docker-compose down

# Restart the WordPress site
restart: down up

# View logs
logs:
	docker-compose logs -f

# Clean everything (DESTRUCTIVE)
clean:
	@echo "⚠️  This will remove all containers, volumes, and data!"
	@read -p "Are you sure? (y/N): " confirm && [ "$$confirm" = "y" ]
	docker-compose down -v
	docker-compose rm -f
	docker volume prune -f
	@echo "Cleanup complete!"

# Fresh install
install: clean up

# Run theme setup
theme-setup:
	@echo "Running EZ Pools theme setup..."
	docker-compose exec wordpress wp eval-file setup.php --allow-root --path=/var/www/html
	@echo "Theme setup complete!"

# Quick status check
status:
	@echo "Container Status:"
	docker-compose ps
	@echo ""
	@echo "If containers are running, visit:"
	@echo "  - Website: http://localhost:8080"
	@echo "  - Admin: http://localhost:8080/wp-admin"

# Open website in browser (works on macOS and Linux)
open:
	@if command -v open >/dev/null 2>&1; then \
		open http://localhost:8080; \
	elif command -v xdg-open >/dev/null 2>&1; then \
		xdg-open http://localhost:8080; \
	else \
		echo "Please open http://localhost:8080 in your browser"; \
	fi

# Database backup
backup:
	@echo "Creating database backup..."
	docker-compose exec db mysqldump -u ezpools_user -pezpools_password ezpools_wp > backup_$(shell date +%Y%m%d_%H%M%S).sql
	@echo "Backup created!"

# Database restore (usage: make restore FILE=backup.sql)
restore:
	@if [ -z "$(FILE)" ]; then \
		echo "Usage: make restore FILE=backup.sql"; \
		exit 1; \
	fi
	@echo "Restoring database from $(FILE)..."
	docker-compose exec -T db mysql -u ezpools_user -pezpools_password ezpools_wp < $(FILE)
	@echo "Database restored!"

# WordPress CLI access
wp:
	docker-compose exec wordpress wp --allow-root --path=/var/www/html $(ARGS)

# Example: make wp ARGS="user list"
# Example: make wp ARGS="plugin install contact-form-7 --activate"
