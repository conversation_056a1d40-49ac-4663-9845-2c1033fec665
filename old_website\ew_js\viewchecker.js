!function(o){o.fn.viewportChecker=function(t){var e={classToAdd:"visible",offset:100,callbackFunction:function(o){}};o.extend(e,t);var i=this,n=o(window).height();this.checkElements=function(){viewportTop=o(window).scrollTop(),viewportBottom=viewportTop+n,i.each(function(){var t=o(this);if(!t.hasClass(e.classToAdd)){var i=Math.round(t.offset().top)+e.offset,n=i+t.height();(i<viewportBottom&&n>viewportTop||i>o(document).height()-e.offset&&o(window).scrollTop()+o(window).height()>o(document).height())&&(t.addClass(e.classToAdd),e.callbackFunction(t))}})},o(window).scroll(this.checkElements),this.checkElements(),o(window).resize(function(o){n=o.currentTarget.innerHeight})}}(jQuery);