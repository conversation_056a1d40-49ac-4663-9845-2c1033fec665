FROM wordpress:6.4-php8.1-apache

# Install additional PHP extensions and tools
RUN apt-get update && apt-get install -y \
    wget \
    unzip \
    curl \
    vim \
    less \
    mariadb-client \
    dos2unix \
    && rm -rf /var/lib/apt/lists/*

# Install WP-CLI
RUN curl -O https://raw.githubusercontent.com/wp-cli/wp-cli/v2.8.1/wp-cli.phar \
    && chmod +x wp-cli.phar \
    && mv wp-cli.phar /usr/local/bin/wp

# Copy theme files first
COPY wp-content/ /var/www/html/wp-content/

# Copy configuration files
COPY wp-config.php /var/www/html/
COPY .htaccess /var/www/html/

# Copy and setup auto-installation script
COPY auto-setup.sh /usr/local/bin/
RUN chmod +x /usr/local/bin/auto-setup.sh

# Set proper permissions
RUN chown -R www-data:www-data /var/www/html \
    && chmod -R 755 /var/www/html

# Create custom entrypoint that handles WordPress auto-setup
COPY docker-entrypoint-custom.sh /usr/local/bin/
RUN chmod +x /usr/local/bin/docker-entrypoint-custom.sh

ENTRYPOINT ["docker-entrypoint-custom.sh"]
CMD ["apache2-foreground"]
