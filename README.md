# EZ Pools WordPress Website

A modern, responsive WordPress theme for EZ Pools - The Better Portable Pool company. This theme showcases portable pool products with a clean, professional design that converts visitors into customers.

## Features

- **Modern Design**: Clean, contemporary layout with blue water-themed color scheme
- **Responsive**: Fully responsive design that works on all devices
- **WordPress Compatible**: Built following WordPress coding standards
- **Custom Post Types**: Pool products and testimonials with custom fields
- **SEO Optimized**: Semantic HTML and proper meta tags
- **Fast Loading**: Optimized CSS and JavaScript
- **Easy Customization**: WordPress Customizer integration

## Quick Setup

### Prerequisites
- PHP 7.4 or higher
- MySQL 5.7 or higher
- WordPress 5.0 or higher
- Web server (Apache/Nginx)

### Installation Steps

1. **Download WordPress**
   ```bash
   # Download and extract WordPress core files
   wget https://wordpress.org/latest.zip
   unzip latest.zip
   # Copy WordPress files to your project directory
   ```

2. **Database Setup**
   ```sql
   # Create database
   CREATE DATABASE ezpools_wp CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
   
   # Create user (optional)
   CREATE USER 'ezpools_user'@'localhost' IDENTIFIED BY 'your_password';
   GRANT ALL PRIVILEGES ON ezpools_wp.* TO 'ezpools_user'@'localhost';
   FLUSH PRIVILEGES;
   ```

3. **Configure WordPress**
   - Copy `wp-config-sample.php` to `wp-config.php`
   - Update database credentials in `wp-config.php`
   - Or use the provided `wp-config.php` and update the database settings

4. **Install WordPress**
   - Visit your site in a browser
   - Follow the WordPress installation wizard
   - Create an admin account

5. **Setup EZ Pools Theme**
   - The theme files are already in `wp-content/themes/ezpools/`
   - Go to WordPress Admin → Appearance → Themes
   - Activate "EZ Pools Modern" theme
   - Or run the setup script: visit `yoursite.com/setup.php`

## Theme Structure

```
wp-content/themes/ezpools/
├── style.css              # Main stylesheet with theme info
├── index.php              # Default template
├── front-page.php         # Homepage template
├── header.php             # Header template
├── footer.php             # Footer template
├── single.php             # Single post template
├── page.php               # Page template
├── functions.php          # Theme functions and features
└── js/
    ├── theme.js           # Main JavaScript functionality
    └── smooth-scroll.js   # Smooth scrolling for navigation
```

## Customization

### Theme Customizer Options
Access via **Appearance → Customize**:

- **Hero Section**: Customize homepage hero title and subtitle
- **Contact Information**: Update phone and email
- **Site Identity**: Upload logo and set site title

### Custom Post Types

#### Pool Products
- **Location**: Admin → Pool Products
- **Fields**: Width, length range, depth, price range
- **Usage**: Automatically displayed on homepage products section

#### Testimonials
- **Location**: Admin → Testimonials  
- **Usage**: Displayed in homepage testimonials slider

### Menus
- **Primary Menu**: Main navigation (Header)
- **Footer Menu**: Footer links (if needed)

## Content Management

### Adding Pool Products
1. Go to **Pool Products → Add New**
2. Enter product title and description
3. Fill in pool specifications (width, length, depth, price)
4. Add featured image (recommended: 400x300px)
5. Publish

### Adding Testimonials
1. Go to **Testimonials → Add New**
2. Enter customer name as title
3. Add testimonial content with customer quote
4. Include customer location/details
5. Publish

### Customizing Pages
- **Homepage**: Controlled by `front-page.php` template
- **About Page**: Standard WordPress page
- **Contact Page**: Standard WordPress page with contact info

## Styling Guide

### Color Scheme
- **Primary Blue**: #0066cc
- **Secondary Blue**: #004499
- **Accent Yellow**: #ffcc00
- **Text**: #333333
- **Light Gray**: #f8f9fa

### Typography
- **Primary Font**: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif
- **Headings**: Bold, blue color scheme
- **Body**: 1.6 line height for readability

### Responsive Breakpoints
- **Desktop**: 1200px+
- **Tablet**: 768px - 1199px
- **Mobile**: 320px - 767px

## Performance Optimization

### Included Optimizations
- Gzip compression (via .htaccess)
- Browser caching headers
- Optimized CSS and JavaScript
- Lazy loading for images
- Minified code

### Recommended Plugins
- **Yoast SEO**: Search engine optimization
- **W3 Total Cache**: Caching and performance
- **Smush**: Image optimization
- **Contact Form 7**: Contact forms

## Security Features

### Built-in Security
- Security headers in .htaccess
- WordPress nonce verification
- Sanitized user inputs
- Escaped outputs

### Recommended Security Plugins
- **Wordfence**: Comprehensive security
- **Limit Login Attempts**: Brute force protection

## Browser Support

- Chrome 60+
- Firefox 60+
- Safari 12+
- Edge 79+
- Internet Explorer 11 (basic support)

## Development

### Local Development Setup
1. Use XAMPP, WAMP, or similar local server
2. Create virtual host pointing to project directory
3. Import database if needed
4. Update wp-config.php for local settings

### File Editing
- CSS: Edit `style.css` for styling changes
- JavaScript: Edit files in `js/` directory
- PHP: Edit template files for layout changes
- Functions: Add custom code to `functions.php`

## Troubleshooting

### Common Issues

**Theme not appearing**
- Check file permissions (755 for directories, 644 for files)
- Verify theme files are in correct directory
- Check for PHP errors in error logs

**Images not displaying**
- Upload images to Media Library
- Check image file paths
- Verify image file permissions

**Menu not working**
- Create menu in Appearance → Menus
- Assign menu to "Primary Menu" location
- Check navigation code in header.php

**Contact form not working**
- Install Contact Form 7 plugin
- Create contact form
- Add shortcode to contact page

## Support

For theme support and customization:
- Check WordPress documentation
- Review theme files for inline comments
- Test changes on staging site first
- Keep backups before making modifications

## License

This theme is built for EZ Pools and follows WordPress GPL licensing.

---

**Note**: Remember to delete `setup.php` after running the initial setup for security purposes.
