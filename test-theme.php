<?php
/**
 * Test script to check if our theme is working
 */

// Include WordPress
require_once('wp-config.php');
require_once('wp-load.php');

echo "=== THEME TEST ===\n\n";

// Force load our theme
$theme_dir = get_template_directory();
echo "Theme directory: $theme_dir\n";

// Check if our CSS file exists and is readable
$css_file = $theme_dir . '/style.css';
if (file_exists($css_file)) {
    echo "✅ CSS file exists\n";
    echo "CSS file size: " . filesize($css_file) . " bytes\n";
    
    // Read first few lines to verify it's our modern CSS
    $css_content = file_get_contents($css_file, false, null, 0, 500);
    if (strpos($css_content, 'EZ Pools Modern') !== false) {
        echo "✅ Modern CSS detected\n";
    } else {
        echo "❌ Old CSS detected\n";
    }
} else {
    echo "❌ CSS file not found\n";
}

// Check functions.php
$functions_file = $theme_dir . '/functions.php';
if (file_exists($functions_file)) {
    echo "✅ Functions.php exists\n";
    
    // Check if our function exists
    if (function_exists('ezpools_scripts')) {
        echo "✅ ezpools_scripts function exists\n";
    } else {
        echo "❌ ezpools_scripts function not found\n";
    }
} else {
    echo "❌ Functions.php not found\n";
}

// Try to manually enqueue our styles
echo "\n=== MANUAL STYLE TEST ===\n";
wp_enqueue_style('ezpools-style-test', get_template_directory_uri() . '/style.css', array(), '2.0.0');
echo "✅ Manually enqueued styles\n";

// Check current URL and template
echo "\n=== URL INFO ===\n";
echo "Current URL: " . home_url() . "\n";
echo "Template directory URI: " . get_template_directory_uri() . "\n";

echo "\n🔄 Try visiting: " . home_url() . "\n";
echo "Or try: " . home_url() . "/?p=4\n";
?>
