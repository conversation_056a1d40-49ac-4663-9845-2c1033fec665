{"version": 2, "customTemplates": [{"name": "blank", "title": "Blank", "postTypes": ["page", "post"]}, {"name": "page-large-header", "title": "Page (Large Header)", "postTypes": ["page"]}, {"name": "single-no-separators", "title": "Single Post (No Separators)", "postTypes": ["post"]}, {"name": "page-no-separators", "title": "Page (No Separators)", "postTypes": ["page"]}], "settings": {"appearanceTools": true, "color": {"duotone": [{"colors": ["#000000", "#ffffff"], "slug": "foreground-and-background", "name": "Foreground and background"}, {"colors": ["#000000", "#ffe2c7"], "slug": "foreground-and-secondary", "name": "Foreground and secondary"}, {"colors": ["#000000", "#f6f6f6"], "slug": "foreground-and-tertiary", "name": "Foreground and tertiary"}, {"colors": ["#1a4548", "#ffffff"], "slug": "primary-and-background", "name": "Primary and background"}, {"colors": ["#1a4548", "#ffe2c7"], "slug": "primary-and-secondary", "name": "Primary and secondary"}, {"colors": ["#1a4548", "#f6f6f6"], "slug": "primary-and-tertiary", "name": "Primary and tertiary"}], "gradients": [{"slug": "vertical-secondary-to-tertiary", "gradient": "linear-gradient(to bottom,var(--wp--preset--color--secondary) 0%,var(--wp--preset--color--tertiary) 100%)", "name": "Vertical secondary to tertiary"}, {"slug": "vertical-secondary-to-background", "gradient": "linear-gradient(to bottom,var(--wp--preset--color--secondary) 0%,var(--wp--preset--color--background) 100%)", "name": "Vertical secondary to background"}, {"slug": "vertical-tertiary-to-background", "gradient": "linear-gradient(to bottom,var(--wp--preset--color--tertiary) 0%,var(--wp--preset--color--background) 100%)", "name": "Vertical tertiary to background"}, {"slug": "diagonal-primary-to-foreground", "gradient": "linear-gradient(to bottom right,var(--wp--preset--color--primary) 0%,var(--wp--preset--color--foreground) 100%)", "name": "Diagonal primary to foreground"}, {"slug": "diagonal-secondary-to-background", "gradient": "linear-gradient(to bottom right,var(--wp--preset--color--secondary) 50%,var(--wp--preset--color--background) 50%)", "name": "Diagonal secondary to background"}, {"slug": "diagonal-background-to-secondary", "gradient": "linear-gradient(to bottom right,var(--wp--preset--color--background) 50%,var(--wp--preset--color--secondary) 50%)", "name": "Diagonal background to secondary"}, {"slug": "diagonal-tertiary-to-background", "gradient": "linear-gradient(to bottom right,var(--wp--preset--color--tertiary) 50%,var(--wp--preset--color--background) 50%)", "name": "Diagonal tertiary to background"}, {"slug": "diagonal-background-to-tertiary", "gradient": "linear-gradient(to bottom right,var(--wp--preset--color--background) 50%,var(--wp--preset--color--tertiary) 50%)", "name": "Diagonal background to tertiary"}], "palette": [{"slug": "foreground", "color": "#000000", "name": "Foreground"}, {"slug": "background", "color": "#ffffff", "name": "Background"}, {"slug": "primary", "color": "#1a4548", "name": "Primary"}, {"slug": "secondary", "color": "#ffe2c7", "name": "Secondary"}, {"slug": "tertiary", "color": "#F6F6F6", "name": "Tertiary"}]}, "custom": {"spacing": {"small": "max(1.25rem, 5vw)", "medium": "clamp(2rem, 8vw, calc(4 * var(--wp--style--block-gap)))", "large": "clamp(4rem, 10vw, 8rem)", "outer": "var(--wp--custom--spacing--small, 1.25rem)"}, "typography": {"font-size": {"huge": "clamp(2.25rem, 4vw, 2.75rem)", "gigantic": "clamp(2.75rem, 6vw, 3.25rem)", "colossal": "clamp(3.25rem, 8vw, 6.25rem)"}, "line-height": {"tiny": 1.15, "small": 1.2, "medium": 1.4, "normal": 1.6}}}, "spacing": {"units": ["%", "px", "em", "rem", "vh", "vw"]}, "typography": {"dropCap": false, "fontFamilies": [{"fontFamily": "-apple-system,BlinkMacSystemFont,\"Segoe UI\",Roboto,Oxygen-Sans,Ubuntu,Cantarell,\"Helvetica Neue\",sans-serif", "name": "System Font", "slug": "system-font"}, {"fontFamily": "\"Source Serif Pro\", serif", "name": "Source Serif Pro", "slug": "source-serif-pro", "fontFace": [{"fontFamily": "Source Serif Pro", "fontWeight": "200 900", "fontStyle": "normal", "fontStretch": "normal", "src": ["file:./assets/fonts/source-serif-pro/SourceSerif4Variable-Roman.ttf.woff2"]}, {"fontFamily": "Source Serif Pro", "fontWeight": "200 900", "fontStyle": "italic", "fontStretch": "normal", "src": ["file:./assets/fonts/source-serif-pro/SourceSerif4Variable-Italic.ttf.woff2"]}]}], "fontSizes": [{"size": "1rem", "slug": "small"}, {"size": "1.125rem", "slug": "medium"}, {"size": "1.75rem", "slug": "large"}, {"size": "clamp(1.75rem, 3vw, 2.25rem)", "slug": "x-large"}]}, "layout": {"contentSize": "650px", "wideSize": "1000px"}}, "styles": {"blocks": {"core/button": {"border": {"radius": "0"}, "color": {"background": "var(--wp--preset--color--primary)", "text": "var(--wp--preset--color--background)"}, "typography": {"fontSize": "var(--wp--preset--font-size--medium)"}}, "core/post-title": {"typography": {"fontFamily": "var(--wp--preset--font-family--source-serif-pro)", "fontWeight": "300", "lineHeight": "var(--wp--custom--typography--line-height--tiny)", "fontSize": "var(--wp--custom--typography--font-size--gigantic)"}}, "core/post-comments": {"spacing": {"padding": {"top": "var(--wp--custom--spacing--small)"}}}, "core/pullquote": {"border": {"width": "1px 0"}}, "core/query-title": {"typography": {"fontFamily": "var(--wp--preset--font-family--source-serif-pro)", "fontWeight": "300", "lineHeight": "var(--wp--custom--typography--line-height--small)", "fontSize": "var(--wp--custom--typography--font-size--gigantic)"}}, "core/quote": {"border": {"width": "1px"}}, "core/site-title": {"typography": {"fontFamily": "var(--wp--preset--font-family--system-font)", "lineHeight": "var(--wp--custom--typography--line-height--normal)", "fontSize": "var(--wp--preset--font-size--medium)", "fontStyle": "italic", "fontWeight": "normal"}}}, "color": {"background": "var(--wp--preset--color--background)", "text": "var(--wp--preset--color--foreground)"}, "elements": {"h1": {"typography": {"fontFamily": "var(--wp--preset--font-family--source-serif-pro)", "fontWeight": "300", "lineHeight": "var(--wp--custom--typography--line-height--tiny)", "fontSize": "var(--wp--custom--typography--font-size--colossal)"}}, "h2": {"typography": {"fontFamily": "var(--wp--preset--font-family--source-serif-pro)", "fontWeight": "300", "lineHeight": "var(--wp--custom--typography--line-height--small)", "fontSize": "var(--wp--custom--typography--font-size--gigantic)"}}, "h3": {"typography": {"fontFamily": "var(--wp--preset--font-family--source-serif-pro)", "fontWeight": "300", "lineHeight": "var(--wp--custom--typography--line-height--tiny)", "fontSize": "var(--wp--custom--typography--font-size--huge)"}}, "h4": {"typography": {"fontFamily": "var(--wp--preset--font-family--source-serif-pro)", "fontWeight": "300", "lineHeight": "var(--wp--custom--typography--line-height--tiny)", "fontSize": "var(--wp--preset--font-size--x-large)"}}, "h5": {"typography": {"fontFamily": "var(--wp--preset--font-family--system-font)", "fontWeight": "700", "textTransform": "uppercase", "lineHeight": "var(--wp--custom--typography--line-height--normal)", "fontSize": "var(--wp--preset--font-size--medium)"}}, "h6": {"typography": {"fontFamily": "var(--wp--preset--font-family--system-font)", "fontWeight": "400", "textTransform": "uppercase", "lineHeight": "var(--wp--custom--typography--line-height--normal)", "fontSize": "var(--wp--preset--font-size--medium)"}}, "link": {"color": {"text": "var(--wp--preset--color--foreground)"}}}, "spacing": {"blockGap": "1.5rem"}, "typography": {"fontFamily": "var(--wp--preset--font-family--system-font)", "lineHeight": "var(--wp--custom--typography--line-height--normal)", "fontSize": "var(--wp--preset--font-size--medium)"}}, "templateParts": [{"name": "header", "title": "Header", "area": "header"}, {"name": "header-large-dark", "title": "Header (Dark, large)", "area": "header"}, {"name": "header-small-dark", "title": "Header (Dark, small)", "area": "header"}, {"name": "footer", "title": "Footer", "area": "footer"}]}