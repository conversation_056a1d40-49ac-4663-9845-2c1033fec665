; PHP Development Configuration - Disable All Caching
; This file disables all caching mechanisms for development

; Disable OPcache completely
opcache.enable=0
opcache.enable_cli=0
opcache.memory_consumption=0
opcache.interned_strings_buffer=0
opcache.max_accelerated_files=0
opcache.revalidate_freq=0
opcache.fast_shutdown=0
opcache.enable_file_override=0
opcache.optimization_level=0
opcache.inherited_hack=0
opcache.dups_fix=0
opcache.blacklist_filename=""

; Disable user cache
apc.enabled=0
apc.enable_cli=0

; Disable file stat cache
realpath_cache_size=0
realpath_cache_ttl=0

; Force file reloading
auto_prepend_file=""
auto_append_file=""

; Development settings
display_errors=On
display_startup_errors=On
log_errors=On
error_reporting=E_ALL
html_errors=On
docref_root=""
docref_ext=""

; Memory and execution settings for development
memory_limit=512M
max_execution_time=300
max_input_time=300

; Disable output buffering
output_buffering=Off
implicit_flush=On

; Session settings (disable session caching)
session.cache_limiter=nocache
session.cache_expire=0

; Disable Zend extensions that might cache
zend_extension=""
