@echo off
REM EZ Pools WordPress Docker Startup Script for Windows (Batch)
REM Double-click this file or run from Command Prompt

echo.
echo 🏊‍♂️ EZ Pools WordPress Docker Setup
echo ======================================
echo.

REM Check if Docker is installed
docker --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Docker Desktop is not installed or not in PATH.
    echo    Please install Docker Desktop from: https://docs.docker.com/desktop/windows/
    pause
    exit /b 1
)

REM Check if Docker is running
docker info >nul 2>&1
if errorlevel 1 (
    echo ❌ Docker Desktop is not running. Please start Docker Desktop first.
    pause
    exit /b 1
)

echo ✅ Docker Desktop is ready!
echo.

REM Check if containers are already running
docker-compose ps --services --filter "status=running" >nul 2>&1
if not errorlevel 1 (
    echo 🔄 WordPress containers are already running.
    echo.
    echo 📍 Access your site at:
    echo    - Website: http://localhost:8080
    echo    - WordPress Admin: http://localhost:8080/wp-admin
    echo    - Username: admin
    echo    - Password: admin123
    echo    - phpMyAdmin: http://localhost:8081
    echo.
    set /p restart="Do you want to restart the containers? (y/N): "
    if /i "%restart%"=="y" (
        echo 🔄 Restarting containers...
        docker-compose down
    ) else (
        echo ✅ Containers are already running. Opening website...
        start http://localhost:8080
        pause
        exit /b 0
    )
)

echo 🚀 Starting EZ Pools WordPress environment...
echo    This will automatically install and configure WordPress with the EZ Pools theme!
echo.

REM Start the containers
docker-compose up -d

echo.
echo ⏳ Waiting for automatic WordPress setup to complete...
echo    This may take 2-3 minutes on first run...

REM Wait for WordPress to be ready (longer for auto-setup)
timeout /t 60 /nobreak >nul

echo.
echo 🎉 EZ Pools WordPress is ready!
echo ================================
echo.
echo ✅ WordPress has been automatically installed and configured with:
echo    - EZ Pools theme activated
echo    - Sample pool products created
echo    - Customer testimonials added
echo    - Navigation menu configured
echo    - Contact information set up
echo.
echo 📍 Access your site at:
echo    🌐 Website: http://localhost:8080
echo    ⚙️  WordPress Admin: http://localhost:8080/wp-admin
echo    👤 Username: admin
echo    🔑 Password: admin123
echo    🗄️  phpMyAdmin: http://localhost:8081
echo.
echo 📋 Useful commands:
echo    - View logs: docker-compose logs -f
echo    - Stop site: docker-compose down
echo    - Restart: docker-compose restart
echo.

REM Open the website in the default browser
start http://localhost:8080

echo ✅ Setup complete! Enjoy your EZ Pools website!
echo.
pause
