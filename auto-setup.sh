#!/bin/bash
# EZ Pools WordPress Auto-Setup Script
# This script sets up the EZ Pools theme, content, and configuration

set -e

echo "🏊‍♂️ Running EZ Pools auto-setup..."

# Set WordPress path
WP_PATH="/var/www/html"
WP_CLI="wp --allow-root --path=$WP_PATH"

# Wait for WordPress to be fully ready
echo "⏳ Waiting for WordPress to be fully ready..."
while ! $WP_CLI core is-installed 2>/dev/null; do
    echo "   WordPress not yet installed, checking if we need to install it..."

    # Check if we can install WordPress
    if ! $WP_CLI core is-installed 2>/dev/null; then
        echo "   Installing WordPress..."
        $WP_CLI core install \
            --url="http://localhost:8080" \
            --title="EZ Pools - The Better Portable Pool" \
            --admin_user="admin" \
            --admin_password="admin123" \
            --admin_email="<EMAIL>" \
            --skip-email 2>/dev/null || true
    fi

    sleep 2
done

echo "✅ WordPress is ready!"

# Activate EZ Pools theme
if $WP_CLI theme is-installed ezpools; then
    echo "🎨 Activating EZ Pools theme..."
    $WP_CLI theme activate ezpools
    echo "✅ EZ Pools theme activated!"
else
    echo "⚠️ EZ Pools theme not found, using default theme"
fi

# Create sample pages
echo "📄 Creating sample pages..."

# Home page (will be set as front page)
if ! $WP_CLI post list --post_type=page --name=home --format=count | grep -q "1"; then
    HOME_PAGE_ID=$($WP_CLI post create --post_type=page --post_title="Home" --post_name="home" --post_status=publish --post_content="This is the homepage content. It will be overridden by front-page.php template." --porcelain)
    echo "✅ Created Home page (ID: $HOME_PAGE_ID)"
else
    HOME_PAGE_ID=$($WP_CLI post list --post_type=page --name=home --field=ID --format=csv)
    echo "ℹ️  Home page already exists (ID: $HOME_PAGE_ID)"
fi

# About page
if ! $WP_CLI post list --post_type=page --name=about --format=count | grep -q "1"; then
    $WP_CLI post create --post_type=page --post_title="About EZ Pools" --post_name="about" --post_status=publish --post_content='<h2>30 Years of Pool Innovation</h2>
<p>Thirty years ago, the term "portable pool" was an oxymoron - after all, how can a swimming pool be portable? But families all across the globe started to quickly realize that what a portable pool means is a full-size swimming pool that can be assembled, by the average family, in less than one hour.</p>

<p>We admit our first pool was pretty basic, and now that original version is copied by China and sold at grocery stores for a handful of dollars. But over the years we have made some dramatic improvements. Today we are able to offer you all the features and benefits of a traditional in-ground lap pool without the hassles and expense typically associated with in-ground lap pools.</p>

<h3>Why Choose EZ Pools?</h3>
<ul>
<li><strong>Made-to-Order:</strong> Every pool is custom made to your specifications</li>
<li><strong>Quick Assembly:</strong> From carton to completion in less than one hour</li>
<li><strong>Affordable:</strong> Usually 1/10th the cost of traditional pools</li>
<li><strong>Durable:</strong> Made from quality American components</li>
<li><strong>Versatile:</strong> Can be on-ground, in-ground, or partially buried</li>
<li><strong>Eco-Friendly:</strong> Sustainable design with organic options</li>
</ul>'
    echo "✅ Created About page"
else
    echo "ℹ️  About page already exists"
fi

# Contact page
if ! $WP_CLI post list --post_type=page --name=contact --format=count | grep -q "1"; then
    $WP_CLI post create --post_type=page --post_title="Contact Us" --post_name="contact" --post_status=publish --post_content='<h2>Get Your Free Quote Today</h2>
<p>Ready to transform your backyard with an EZ Pool? Contact us today for a free, no-obligation quote. Our team will help you find the perfect pool for your needs and budget.</p>

<div class="contact-info">
<h3>Contact Information</h3>
<p><strong>Phone:</strong> (855) 4EZ-POOL<br>
<strong>Email:</strong> <EMAIL><br>
<strong>Hours:</strong> Monday-Friday 8AM-6PM EST</p>
</div>

<h3>Why Call Us?</h3>
<ul>
<li>Free consultation and quote</li>
<li>Expert advice on pool sizing</li>
<li>Custom design options</li>
<li>Assembly guidance</li>
<li>Ongoing support</li>
</ul>'
    echo "✅ Created Contact page"
else
    echo "ℹ️  Contact page already exists"
fi

# Set front page
echo "🏠 Setting front page..."
$WP_CLI option update show_on_front page
$WP_CLI option update page_on_front $HOME_PAGE_ID
echo "✅ Set Home as front page"

# Create sample pool products
echo "🏊‍♂️ Creating sample pool products..."

# 7x12 Lap Pool
if ! $WP_CLI post list --post_type=pool_product --s="7x12 Lap Pool" --format=count | grep -q "1"; then
    POOL_ID=$($WP_CLI post create --post_type=pool_product --post_title="7x12 Lap Pool" --post_status=publish --post_content='<p>Perfect starter lap pool for fitness enthusiasts. Compact design fits in smaller backyards while providing excellent swimming experience.</p>
<h3>Features:</h3>
<ul>
<li>7 feet wide by 12 feet long</li>
<li>4 feet deep standard</li>
<li>Made from durable American components</li>
<li>Quick 1-hour assembly</li>
<li>Perfect for daily fitness routines</li>
</ul>' --post_excerpt="Compact lap pool perfect for fitness and smaller spaces. Quick assembly, durable construction." --porcelain)
    
    $WP_CLI post meta update $POOL_ID _pool_width "7"
    $WP_CLI post meta update $POOL_ID _pool_length_min "12"
    $WP_CLI post meta update $POOL_ID _pool_length_max "12"
    $WP_CLI post meta update $POOL_ID _pool_depth "4"
    $WP_CLI post meta update $POOL_ID _pool_price_range "\$3,500 - \$4,500"
    echo "✅ Created 7x12 Lap Pool"
else
    echo "ℹ️  7x12 Lap Pool already exists"
fi

# 12x17 Family Pool
if ! $WP_CLI post list --post_type=pool_product --s="12x17 Family Pool" --format=count | grep -q "1"; then
    POOL_ID=$($WP_CLI post create --post_type=pool_product --post_title="12x17 Family Pool" --post_status=publish --post_content='<p>Our most popular family pool size. Perfect balance of swimming space and backyard fit. Great for families with children.</p>
<h3>Features:</h3>
<ul>
<li>12 feet wide by 17 feet long</li>
<li>4 feet deep standard (custom depths available)</li>
<li>Spacious enough for family fun</li>
<li>Easy maintenance</li>
<li>Can be partially buried</li>
</ul>' --post_excerpt="Most popular family pool size. Perfect for recreation and family fun." --porcelain)
    
    $WP_CLI post meta update $POOL_ID _pool_width "12"
    $WP_CLI post meta update $POOL_ID _pool_length_min "17"
    $WP_CLI post meta update $POOL_ID _pool_length_max "17"
    $WP_CLI post meta update $POOL_ID _pool_depth "4"
    $WP_CLI post meta update $POOL_ID _pool_price_range "\$6,500 - \$8,500"
    echo "✅ Created 12x17 Family Pool"
else
    echo "ℹ️  12x17 Family Pool already exists"
fi

# 22x32 Giant Pool
if ! $WP_CLI post list --post_type=pool_product --s="22x32 Giant Pool" --format=count | grep -q "1"; then
    POOL_ID=$($WP_CLI post create --post_type=pool_product --post_title="22x32 Giant Pool" --post_status=publish --post_content='<p>Our largest standard pool for maximum swimming space. Perfect for large families, events, or commercial use.</p>
<h3>Features:</h3>
<ul>
<li>22 feet wide by 32 feet long</li>
<li>Variable depth options</li>
<li>Commercial-grade construction</li>
<li>Suitable for events and gatherings</li>
<li>Professional installation recommended</li>
</ul>' --post_excerpt="Our largest standard pool for maximum space and commercial applications." --porcelain)
    
    $WP_CLI post meta update $POOL_ID _pool_width "22"
    $WP_CLI post meta update $POOL_ID _pool_length_min "32"
    $WP_CLI post meta update $POOL_ID _pool_length_max "32"
    $WP_CLI post meta update $POOL_ID _pool_depth "4-6"
    $WP_CLI post meta update $POOL_ID _pool_price_range "\$15,000 - \$20,000"
    echo "✅ Created 22x32 Giant Pool"
else
    echo "ℹ️  22x32 Giant Pool already exists"
fi

# Create sample testimonials
echo "💬 Creating sample testimonials..."

if ! $WP_CLI post list --post_type=testimonial --s="Amazing Pool Experience" --format=count | grep -q "1"; then
    $WP_CLI post create --post_type=testimonial --post_title="Amazing Pool Experience" --post_status=publish --post_content='<p>"We were skeptical about a portable pool, but EZ Pools exceeded all our expectations. The assembly was incredibly easy - my husband and I had it up and running in 45 minutes. Our kids love it, and we use it every day for exercise. Best investment we'\''ve made for our backyard!"</p>
<p><strong>- Sarah Johnson, Florida</strong></p>'
    echo "✅ Created testimonial 1"
else
    echo "ℹ️  Testimonial 1 already exists"
fi

if ! $WP_CLI post list --post_type=testimonial --s="Perfect for Our Training Facility" --format=count | grep -q "1"; then
    $WP_CLI post create --post_type=testimonial --post_title="Perfect for Our Training Facility" --post_status=publish --post_content='<p>"As a swim coach, I needed a pool that could be set up quickly for our training camp. EZ Pools delivered exactly what we needed. The 25-meter pool was perfect for our Olympic hopefuls, and the quality is outstanding. We'\''ve used it for three seasons now without any issues."</p>
<p><strong>- Coach Mike Rodriguez, California</strong></p>'
    echo "✅ Created testimonial 2"
else
    echo "ℹ️  Testimonial 2 already exists"
fi

# Create navigation menu
echo "🧭 Setting up navigation menu..."
MENU_ID=$($WP_CLI menu create "Primary Menu" --porcelain 2>/dev/null || $WP_CLI menu list --fields=term_id,name --format=csv | grep "Primary Menu" | cut -d, -f1)

if [ ! -z "$MENU_ID" ]; then
    # Add menu items
    $WP_CLI menu item add-custom $MENU_ID "Home" "http://localhost:8080/" --porcelain 2>/dev/null || true
    $WP_CLI menu item add-custom $MENU_ID "Products" "http://localhost:8080/#products" --porcelain 2>/dev/null || true
    $WP_CLI menu item add-post $MENU_ID $($WP_CLI post list --post_type=page --name=about --field=ID --format=csv) --porcelain 2>/dev/null || true
    $WP_CLI menu item add-post $MENU_ID $($WP_CLI post list --post_type=page --name=contact --field=ID --format=csv) --porcelain 2>/dev/null || true
    
    # Assign menu to location
    $WP_CLI menu location assign $MENU_ID primary
    echo "✅ Created and assigned primary menu"
else
    echo "⚠️ Could not create menu"
fi

# Set theme customizer options
echo "🎨 Setting theme customizer options..."
$WP_CLI option update theme_mods_ezpools '{"hero_title":"The Better Portable Pool","hero_subtitle":"Custom portable pools made-to-order. From carton to completion in less than one hour. All the features of traditional pools at 1\/10th the cost.","contact_phone":"(855) 4EZ-POOL","contact_email":"<EMAIL>"}' --format=json

# Set site title and tagline
$WP_CLI option update blogname "EZ Pools - The Better Portable Pool"
$WP_CLI option update blogdescription "Custom portable pools made-to-order"

echo "✅ EZ Pools auto-setup completed successfully!"
echo ""
echo "🎉 Your EZ Pools website is ready with:"
echo "   - EZ Pools theme activated"
echo "   - Sample pool products"
echo "   - Customer testimonials"
echo "   - Navigation menu"
echo "   - Contact information"
echo "   - About page content"
echo ""
