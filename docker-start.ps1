# EZ Pools WordPress Docker Startup Script for Windows
# Run this script in PowerShell to start the WordPress environment

Write-Host "🏊‍♂️ EZ Pools WordPress Docker Setup" -ForegroundColor Cyan
Write-Host "======================================" -ForegroundColor Cyan

# Check if Docker Desktop is installed and running
try {
    $dockerVersion = docker --version 2>$null
    if (-not $dockerVersion) {
        throw "Docker not found"
    }
    Write-Host "✅ Docker is installed: $dockerVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ Docker Desktop is not installed or not in PATH." -ForegroundColor Red
    Write-Host "   Please install Docker Desktop from: https://docs.docker.com/desktop/windows/" -ForegroundColor Yellow
    exit 1
}

# Check if Docker is running
try {
    docker info 2>$null | Out-Null
    Write-Host "✅ Docker Desktop is running!" -ForegroundColor Green
} catch {
    Write-Host "❌ Docker Desktop is not running. Please start Docker Desktop first." -ForegroundColor Red
    exit 1
}

Write-Host ""

# Check if containers are already running
$runningContainers = docker-compose ps --services --filter "status=running" 2>$null
if ($runningContainers) {
    Write-Host "🔄 WordPress containers are already running." -ForegroundColor Yellow
    Write-Host ""
    Write-Host "📍 Access your site at:" -ForegroundColor Cyan
    Write-Host "   - Website: http://localhost:8080" -ForegroundColor White
    Write-Host "   - WordPress Admin: http://localhost:8080/wp-admin" -ForegroundColor White
    Write-Host "   - Username: admin" -ForegroundColor White
    Write-Host "   - Password: admin123" -ForegroundColor White
    Write-Host "   - phpMyAdmin: http://localhost:8081" -ForegroundColor White
    Write-Host ""
    
    $restart = Read-Host "Do you want to restart the containers? (y/N)"
    if ($restart -eq "y" -or $restart -eq "Y") {
        Write-Host "🔄 Restarting containers..." -ForegroundColor Yellow
        docker-compose down
    } else {
        Write-Host "✅ Containers are already running. Nothing to do!" -ForegroundColor Green
        Start-Process "http://localhost:8080"
        exit 0
    }
}

Write-Host "🚀 Starting EZ Pools WordPress environment..." -ForegroundColor Cyan
Write-Host "   This will automatically install and configure WordPress with the EZ Pools theme!" -ForegroundColor Green
Write-Host ""

# Start the containers
docker-compose up -d

Write-Host ""
Write-Host "⏳ Waiting for automatic WordPress setup to complete..." -ForegroundColor Yellow
Write-Host "   This may take 1-2 minutes on first run..." -ForegroundColor Yellow
Write-Host "   You can monitor progress with: docker-compose logs -f setup" -ForegroundColor Gray

# Wait for WordPress to be ready (longer timeout for auto-setup)
$timeout = 180
$counter = 0
do {
    if ($counter -ge $timeout) {
        Write-Host ""
        Write-Host "❌ Timeout waiting for WordPress to start" -ForegroundColor Red
        Write-Host "   Check logs with: docker-compose logs" -ForegroundColor Yellow
        exit 1
    }
    Write-Host "." -NoNewline -ForegroundColor Yellow
    Start-Sleep -Seconds 2
    $counter += 2
    
    try {
        $response = Invoke-WebRequest -Uri "http://localhost:8080" -TimeoutSec 1 -ErrorAction SilentlyContinue
        $isReady = $response.StatusCode -eq 200
    } catch {
        $isReady = $false
    }
} while (-not $isReady)

Write-Host ""
Write-Host ""
Write-Host "🎉 EZ Pools WordPress is ready!" -ForegroundColor Green
Write-Host "================================" -ForegroundColor Green
Write-Host ""
Write-Host "✅ WordPress has been automatically installed and configured with:" -ForegroundColor Green
Write-Host "   - EZ Pools theme activated" -ForegroundColor White
Write-Host "   - Sample pool products created" -ForegroundColor White
Write-Host "   - Customer testimonials added" -ForegroundColor White
Write-Host "   - Navigation menu configured" -ForegroundColor White
Write-Host "   - Contact information set up" -ForegroundColor White
Write-Host ""
Write-Host "📍 Access your site at:" -ForegroundColor Cyan
Write-Host "   🌐 Website: http://localhost:8080" -ForegroundColor White
Write-Host "   ⚙️  WordPress Admin: http://localhost:8080/wp-admin" -ForegroundColor White
Write-Host "   👤 Username: admin" -ForegroundColor White
Write-Host "   🔑 Password: admin123" -ForegroundColor White
Write-Host "   🗄️  phpMyAdmin: http://localhost:8081" -ForegroundColor White
Write-Host ""
Write-Host "📋 Useful PowerShell commands:" -ForegroundColor Cyan
Write-Host "   - View logs: docker-compose logs -f" -ForegroundColor White
Write-Host "   - Stop site: docker-compose down" -ForegroundColor White
Write-Host "   - Restart: docker-compose restart" -ForegroundColor White
Write-Host "   - Clean install: docker-compose down -v; docker-compose up -d" -ForegroundColor White
Write-Host ""

# Open the website in the default browser
try {
    Start-Process "http://localhost:8080"
    Write-Host "🌐 Opening website in your default browser..." -ForegroundColor Green
} catch {
    Write-Host "💡 Please open http://localhost:8080 in your browser to view the site" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "✅ Setup complete! Enjoy your EZ Pools website!" -ForegroundColor Green
