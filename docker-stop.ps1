# EZ Pools WordPress Docker Stop Script for Windows
# Run this script in PowerShell to stop the WordPress environment

Write-Host "🛑 Stopping EZ Pools WordPress Docker Environment" -ForegroundColor Yellow
Write-Host "=================================================" -ForegroundColor Yellow

try {
    # Check if containers are running
    $runningContainers = docker-compose ps --services --filter "status=running" 2>$null
    
    if (-not $runningContainers) {
        Write-Host "ℹ️  No containers are currently running." -ForegroundColor Blue
        exit 0
    }
    
    Write-Host "🔄 Stopping WordPress containers..." -ForegroundColor Yellow
    docker-compose down
    
    Write-Host ""
    Write-Host "✅ EZ Pools WordPress environment stopped successfully!" -ForegroundColor Green
    Write-Host ""
    Write-Host "📋 To start again, run:" -ForegroundColor Cyan
    Write-Host "   .\docker-start.ps1" -ForegroundColor White
    Write-Host ""
    Write-Host "📋 To completely remove (including data):" -ForegroundColor Cyan
    Write-Host "   docker-compose down -v" -ForegroundColor White
    
} catch {
    Write-Host "❌ Error stopping containers: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "   Try running: docker-compose down" -ForegroundColor Yellow
}
