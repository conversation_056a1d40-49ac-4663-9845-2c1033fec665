<!DOCTYPE html><html lang="en-US"><head><title>EZ Pool Cover</title><link rel="shortcut icon" href="/favicon.ico" /><meta http-equiv="Content-type" content="text/html;charset=UTF-8" /><meta name="viewport" content="width=300" /><meta name="description" content="Secure your pool for the winter, from debris and bugs or even unwanted guests. Designed using similar material as an EZ Pool, the pool cover makes a great portable pool addition. Call 855-439-7665." /><meta property="og:description" content="Secure your pool for the winter, from debris and bugs or even unwanted guests. Designed using similar material as an EZ Pool, the pool cover makes a great portable pool addition. Call 855-439-7665." /><meta name="generator" content="EverWeb 4.0.1 (2884)" /><meta name="buildDate" content="Thursday, June 19, 2025" /><meta property="og:url" content="https://4ezpool.com/pool-cover.html" /><meta property="og:title" content="EZ Pool Cover" /><meta property="og:type" content="website" /><link rel="stylesheet" type="text/css" href="ew_css/textstyles.css?3833225815" /><link rel="stylesheet" type="text/css" href="ew_css/responsive.css?3833225815" /><link rel="stylesheet" type="text/css" href="ew_css/globaltextstyles.css?3833225815" /><script type="text/javascript" src="ew_js/imageCode.js" defer></script><script language="JavaScript">

<!--

function MM_swapImgRestore() { //v3.0

  var i,x,a=document.MM_sr; for(i=0;a&&i<a.length&&(x=a[i])&&x.oSrc;i++) x.src=x.oSrc;

}



function MM_preloadImages() { //v3.0

  var d=document; if(d.images){ if(!d.MM_p) d.MM_p=new Array();

    var i,j=d.MM_p.length,a=MM_preloadImages.arguments; for(i=0; i<a.length; i++)

    if (a[i].indexOf("#")!=0){ d.MM_p[j]=new Image; d.MM_p[j++].src=a[i];}}

}



function MM_findObj(n, d) { //v4.0

  var p,i,x;  if(!d) d=document; if((p=n.indexOf("?"))>0&&parent.frames.length) {

    d=parent.frames[n.substring(p+1)].document; n=n.substring(0,p);}

  if(!(x=d[n])&&d.all) x=d.all[n]; for (i=0;!x&&i<d.forms.length;i++) x=d.forms[i][n];

  for(i=0;!x&&d.layers&&i<d.layers.length;i++) x=MM_findObj(n,d.layers[i].document);

  if(!x && document.getElementById) x=document.getElementById(n); return x;

}



function MM_swapImage() { //v3.0

  var i,j=0,x,a=MM_swapImage.arguments; document.MM_sr=new Array; for(i=0;i<(a.length-2);i+=3)

   if ((x=MM_findObj(a[i]))!=null){document.MM_sr[j++]=x; if(!x.oSrc) x.oSrc=x.src; x.src=a[i+2];}

}


////////////////////////////////////////////////////////////////

    // make new window function

    ////////////////////////////////////////////////////////////////

    function createNewWin(newURL)

    {

        var newWindow;



        newWindow = window.open(newURL,"","scrollbars=1,resizable=1,height=400,width=400");

        

    }

	//  -->

  </script><style type="text/css">a img {border:0px;}body {background-color: #FFFEFE;margin: 0px auto;}div.container {margin: 0px auto;width: 300px;height: 466px;}</style></head><body><div class="container" style="height:466px"><div class="content" data-minheight="300"><div style="position:relative"><div class="shape_0" style="left:0px;top:98px;width:299px;height:224px;z-index:0;position: absolute;"><img src="images/pool-cover/pool rainbow 010.jpg" height="224" width="299" alt="EZ Pool Cover" data-src2x="images/pool-cover/<NAME_EMAIL>" srcset="images/pool-cover/pool%20rainbow%20010.jpg 1x, images/pool-cover/<EMAIL> 2x" /></div></div><div style="position:relative"><div class="shape_1" style="left:20px;top:22px;width:245px;height:71px;z-index:1;position: absolute;"><div class="paraWrap" style="padding: 0px 2.16px 0px 2.16px; "><p class="para52"><span style="line-height: 13.2px;" class="Style219">Made-to-Order EZ Pool</span></p><p class="para131"><span style="line-height: 19.8px;" class="Style220">Cover for Full Pool</span></p><p class="para52"><span style="line-height: 15.4px;" class="Style229">Not Recommended for Empty, Drained or Decked Pools</span></p></div></div></div><div style="position:relative"><div class="shape_2" style="left:13px;top:344px;width:279px;height:122px;z-index:2;position: absolute;"><p class="para4"><span style="line-height: 21.74584px;" class="Style5">Secure your pool for the winter, from debris and bugs or even unwanted guests. Designed using similar material as an EZ Pool, the pool cover makes a great portable pool addition.</span></p></div></div></div><footer data-top='466' data-height='0'></footer></div></body></html>