{"$schema": "https://schemas.wp.org/trunk/theme.json", "version": 2, "title": "Grapes", "settings": {"color": {"palette": [{"color": "#E1E1C7", "name": "Base", "slug": "base"}, {"color": "#000000", "name": "Contrast", "slug": "contrast"}, {"color": "#214F31", "name": "Primary", "slug": "primary"}, {"color": "#000000", "name": "Secondary", "slug": "secondary"}, {"color": "#F0EBD2", "name": "Tertiary", "slug": "tertiary"}]}}, "styles": {"blocks": {"core/post-comments": {"elements": {"link": {":hover": {"typography": {"textDecoration": "underline dashed"}}}}}, "core/post-date": {"typography": {"fontFamily": "var(--wp--preset--font-family--source-serif-pro)", "fontStyle": "italic"}}, "core/post-terms": {"typography": {"fontFamily": "var(--wp--preset--font-family--source-serif-pro)", "fontStyle": "italic"}}, "core/site-title": {"typography": {"textTransform": "lowercase"}}}, "elements": {"button": {"border": {"radius": "9999px"}, "color": {"background": "var(--wp--preset--color--primary)", "text": "var(--wp--preset--color--base)"}, ":visited": {"color": {"text": "var(--wp--preset--color--base)"}}}, "heading": {"typography": {"fontFamily": "var(--wp--preset--font-family--source-serif-pro)", "fontWeight": "600"}}, "link": {":hover": {"typography": {"textDecoration": "underline dashed"}}}}}}