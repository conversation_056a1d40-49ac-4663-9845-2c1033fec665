{"$schema": "https://schemas.wp.org/trunk/theme.json", "version": 2, "title": "<PERSON><PERSON><PERSON>", "settings": {"color": {"duotone": [{"colors": ["#FF99FF", "#FFFF99", "#99FFFF"], "name": "Default filter", "slug": "default-filter"}], "gradients": [{"gradient": "linear-gradient(135deg, var(--wp--preset--color--primary) 0%, var(--wp--preset--color--secondary) 50%, var(--wp--preset--color--tertiary) 100%)", "name": "Primary to Secondary to Tertiary", "slug": "primary-secondary-tertiary"}, {"gradient": "linear-gradient(135deg, var(--wp--preset--color--primary) 0%, var(--wp--preset--color--secondary) 50%, var(--wp--preset--color--tertiary) 100%) fixed", "name": "Primary to Secondary to Tertiary Fixed", "slug": "primary-secondary-tertiary-fixed"}, {"gradient": "linear-gradient(135deg, var(--wp--preset--color--tertiary) 0%, var(--wp--preset--color--secondary) 50%, var(--wp--preset--color--primary) 100%) fixed", "name": "Tertiary to Secondary to Primary Fixed", "slug": "tertiary-secondary-primary-fixed"}], "palette": [{"color": "#FFFFFF", "name": "Base", "slug": "base"}, {"color": "#000000", "name": "Contrast", "slug": "contrast"}, {"color": "#FFCCFF", "name": "Primary", "slug": "primary"}, {"color": "#FFFFCC", "name": "Secondary", "slug": "secondary"}, {"color": "#CCFFFF", "name": "Tertiary", "slug": "tertiary"}]}, "typography": {"fontSizes": [{"fluid": false, "size": "0.75rem", "slug": "x-small"}, {"fluid": {"min": "0.875rem", "max": "1rem"}, "size": "1rem", "slug": "small"}, {"fluid": {"min": "1rem", "max": "1.125rem"}, "size": "1.125rem", "slug": "medium"}, {"fluid": {"min": "1.5rem", "max": "1.75rem"}, "size": "1.75rem", "slug": "large"}, {"fluid": {"min": "2rem", "max": "2.25rem"}, "size": "2.25rem", "slug": "x-large"}, {"fluid": {"min": "2.5rem", "max": "2.75rem"}, "size": "2.75rem", "slug": "xx-large"}]}}, "styles": {"blocks": {"core/comments": {"elements": {"link": {":active": {"color": {"text": "var(--wp--preset--color--contrast)"}}}}}, "core/comment-author-name": {"typography": {"fontSize": "var(--wp--preset--font-size--medium)", "textTransform": "initial"}}, "core/comment-content": {"typography": {"fontSize": "var(--wp--preset--font-size--medium)", "textTransform": "initial"}}, "core/navigation": {"typography": {"fontSize": "var(--wp--preset--font-size--small)", "fontWeight": "500", "textTransform": "uppercase"}}, "core/post-content": {"elements": {"link": {"color": {"text": "var(--wp--preset--color--contrast)"}, ":active": {"color": {"text": "var(--wp--preset--color--contrast)"}}}}}, "core/post-date": {"typography": {"textTransform": "uppercase"}}, "core/post-featured-image": {"filter": {"duotone": "var(--wp--preset--duotone--default-filter)"}, "border": {"color": "var(--wp--preset--color--tertiary)", "style": "solid"}}, "core/post-title": {"typography": {"fontWeight": "500", "textTransform": "uppercase"}}, "core/site-title": {"typography": {"fontWeight": "500"}}, "core/template-part": {"typography": {"fontSize": "var(--wp--preset--font-size--x-small)", "fontWeight": "400", "textTransform": "uppercase"}}}, "color": {"gradient": "var(--wp--preset--gradient--primary-secondary-tertiary)"}, "elements": {"button": {"border": {"color": "var(--wp--preset--color--contrast)", "radius": "99999px", "style": "solid", "width": "2px"}, "color": {"background": "var(--wp--preset--color--base)", "gradient": "var(--wp--preset--gradient--primary-secondary-tertiary-fixed)", "text": "var(--wp--preset--color--contrast)"}, "typography": {"fontSize": "var(--wp--preset--font-size--x-small)", "fontWeight": "400", "textTransform": "uppercase"}, ":hover": {"color": {"gradient": "var(--wp--preset--gradient--tertiary-secondary-primary-fixed)", "text": "var(--wp--preset--color--contrast)"}}, ":focus": {"color": {"background": "var(--wp--preset--color--contrast)", "gradient": "none"}}, ":active": {"color": {"background": "var(--wp--preset--color--contrast)", "gradient": "none"}}}, "heading": {"typography": {"fontWeight": "500"}}}, "typography": {"fontSize": "var(--wp--preset--font-size--small)", "fontFamily": "var(--wp--preset--font-family--inter)"}}}