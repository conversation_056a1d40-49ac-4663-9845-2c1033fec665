<?php
/**
 * EZ Pools WordPress Setup Script
 * Run this after WordPress installation to set up the theme and sample content
 */

// Check if WordPress is installed
if (!file_exists('wp-config.php')) {
    die('WordPress is not installed. Please install WordPress first.');
}

// Include WordPress
require_once('wp-config.php');
require_once('wp-load.php');

// Check if user is logged in as admin
if (!current_user_can('administrator')) {
    die('You must be logged in as an administrator to run this setup.');
}

echo "<h1>EZ Pools WordPress Setup</h1>";

// Activate the EZ Pools theme
$theme = 'ezpools';
if (file_exists(get_theme_root() . '/' . $theme . '/style.css')) {
    switch_theme($theme);
    echo "<p>✓ EZ Pools theme activated</p>";
} else {
    echo "<p>✗ EZ Pools theme not found</p>";
}

// Create sample pages
$pages = array(
    'Home' => array(
        'post_title' => 'Home',
        'post_content' => 'This is the homepage content. It will be overridden by front-page.php template.',
        'post_status' => 'publish',
        'post_type' => 'page',
        'post_name' => 'home'
    ),
    'About' => array(
        'post_title' => 'About EZ Pools',
        'post_content' => '<h2>30 Years of Pool Innovation</h2>
<p>Thirty years ago, the term "portable pool" was an oxymoron - after all, how can a swimming pool be portable? But families all across the globe started to quickly realize that what a portable pool means is a full-size swimming pool that can be assembled, by the average family, in less than one hour.</p>

<p>We admit our first pool was pretty basic, and now that original version is copied by China and sold at grocery stores for a handful of dollars. But over the years we have made some dramatic improvements. Today we are able to offer you all the features and benefits of a traditional in-ground lap pool without the hassles and expense typically associated with in-ground lap pools.</p>

<h3>Why Choose EZ Pools?</h3>
<ul>
<li><strong>Made-to-Order:</strong> Every pool is custom made to your specifications</li>
<li><strong>Quick Assembly:</strong> From carton to completion in less than one hour</li>
<li><strong>Affordable:</strong> Usually 1/10th the cost of traditional pools</li>
<li><strong>Durable:</strong> Made from quality American components</li>
<li><strong>Versatile:</strong> Can be on-ground, in-ground, or partially buried</li>
<li><strong>Eco-Friendly:</strong> Sustainable design with organic options</li>
</ul>',
        'post_status' => 'publish',
        'post_type' => 'page',
        'post_name' => 'about'
    ),
    'Contact' => array(
        'post_title' => 'Contact Us',
        'post_content' => '<h2>Get Your Free Quote Today</h2>
<p>Ready to transform your backyard with an EZ Pool? Contact us today for a free, no-obligation quote. Our team will help you find the perfect pool for your needs and budget.</p>

<div class="contact-info">
<h3>Contact Information</h3>
<p><strong>Phone:</strong> (855) 4EZ-POOL<br>
<strong>Email:</strong> <EMAIL><br>
<strong>Hours:</strong> Monday-Friday 8AM-6PM EST</p>
</div>

<h3>Why Call Us?</h3>
<ul>
<li>Free consultation and quote</li>
<li>Expert advice on pool sizing</li>
<li>Custom design options</li>
<li>Assembly guidance</li>
<li>Ongoing support</li>
</ul>',
        'post_status' => 'publish',
        'post_type' => 'page',
        'post_name' => 'contact'
    )
);

foreach ($pages as $page_name => $page_data) {
    $existing_page = get_page_by_path($page_data['post_name']);
    if (!$existing_page) {
        $page_id = wp_insert_post($page_data);
        if ($page_id) {
            echo "<p>✓ Created page: $page_name</p>";
        } else {
            echo "<p>✗ Failed to create page: $page_name</p>";
        }
    } else {
        echo "<p>- Page already exists: $page_name</p>";
    }
}

// Set front page
$front_page = get_page_by_path('home');
if ($front_page) {
    update_option('show_on_front', 'page');
    update_option('page_on_front', $front_page->ID);
    echo "<p>✓ Set front page to Home</p>";
}

// Create sample pool products
$pool_products = array(
    array(
        'post_title' => '7x12 Lap Pool',
        'post_content' => '<p>Perfect starter lap pool for fitness enthusiasts. Compact design fits in smaller backyards while providing excellent swimming experience.</p>
<h3>Features:</h3>
<ul>
<li>7 feet wide by 12 feet long</li>
<li>4 feet deep standard</li>
<li>Made from durable American components</li>
<li>Quick 1-hour assembly</li>
<li>Perfect for daily fitness routines</li>
</ul>',
        'post_excerpt' => 'Compact lap pool perfect for fitness and smaller spaces. Quick assembly, durable construction.',
        'post_status' => 'publish',
        'post_type' => 'pool_product',
        'meta' => array(
            '_pool_width' => '7',
            '_pool_length_min' => '12',
            '_pool_length_max' => '12',
            '_pool_depth' => '4',
            '_pool_price_range' => '$3,500 - $4,500'
        )
    ),
    array(
        'post_title' => '12x17 Family Pool',
        'post_content' => '<p>Our most popular family pool size. Perfect balance of swimming space and backyard fit. Great for families with children.</p>
<h3>Features:</h3>
<ul>
<li>12 feet wide by 17 feet long</li>
<li>4 feet deep standard (custom depths available)</li>
<li>Spacious enough for family fun</li>
<li>Easy maintenance</li>
<li>Can be partially buried</li>
</ul>',
        'post_excerpt' => 'Most popular family pool size. Perfect for recreation and family fun.',
        'post_status' => 'publish',
        'post_type' => 'pool_product',
        'meta' => array(
            '_pool_width' => '12',
            '_pool_length_min' => '17',
            '_pool_length_max' => '17',
            '_pool_depth' => '4',
            '_pool_price_range' => '$6,500 - $8,500'
        )
    ),
    array(
        'post_title' => '22x32 Giant Pool',
        'post_content' => '<p>Our largest standard pool for maximum swimming space. Perfect for large families, events, or commercial use.</p>
<h3>Features:</h3>
<ul>
<li>22 feet wide by 32 feet long</li>
<li>Variable depth options</li>
<li>Commercial-grade construction</li>
<li>Suitable for events and gatherings</li>
<li>Professional installation recommended</li>
</ul>',
        'post_excerpt' => 'Our largest standard pool for maximum space and commercial applications.',
        'post_status' => 'publish',
        'post_type' => 'pool_product',
        'meta' => array(
            '_pool_width' => '22',
            '_pool_length_min' => '32',
            '_pool_length_max' => '32',
            '_pool_depth' => '4-6',
            '_pool_price_range' => '$15,000 - $20,000'
        )
    )
);

foreach ($pool_products as $product) {
    $meta = $product['meta'];
    unset($product['meta']);
    
    $product_id = wp_insert_post($product);
    if ($product_id) {
        foreach ($meta as $key => $value) {
            update_post_meta($product_id, $key, $value);
        }
        echo "<p>✓ Created pool product: {$product['post_title']}</p>";
    } else {
        echo "<p>✗ Failed to create pool product: {$product['post_title']}</p>";
    }
}

// Create navigation menu
$menu_name = 'Primary Menu';
$menu_exists = wp_get_nav_menu_object($menu_name);

if (!$menu_exists) {
    $menu_id = wp_create_nav_menu($menu_name);
    
    // Add menu items
    $menu_items = array(
        array('title' => 'Home', 'url' => home_url('/'), 'menu-item-status' => 'publish'),
        array('title' => 'Products', 'url' => home_url('/#products'), 'menu-item-status' => 'publish'),
        array('title' => 'About', 'url' => home_url('/about'), 'menu-item-status' => 'publish'),
        array('title' => 'Contact', 'url' => home_url('/contact'), 'menu-item-status' => 'publish')
    );
    
    foreach ($menu_items as $item) {
        wp_update_nav_menu_item($menu_id, 0, $item);
    }
    
    // Set menu location
    $locations = get_theme_mod('nav_menu_locations');
    $locations['primary'] = $menu_id;
    set_theme_mod('nav_menu_locations', $locations);
    
    echo "<p>✓ Created primary navigation menu</p>";
} else {
    echo "<p>- Primary menu already exists</p>";
}

// Set theme customizer defaults
set_theme_mod('hero_title', 'The Better Portable Pool');
set_theme_mod('hero_subtitle', 'Custom portable pools made-to-order. From carton to completion in less than one hour. All the features of traditional pools at 1/10th the cost.');
set_theme_mod('contact_phone', '(855) 4EZ-POOL');
set_theme_mod('contact_email', '<EMAIL>');

echo "<p>✓ Set theme customizer defaults</p>";

echo "<h2>Setup Complete!</h2>";
echo "<p>Your EZ Pools WordPress site is now ready. You can:</p>";
echo "<ul>";
echo "<li>Visit your <a href='" . home_url() . "'>homepage</a> to see the new theme</li>";
echo "<li>Go to <a href='" . admin_url('customize.php') . "'>Appearance > Customize</a> to modify settings</li>";
echo "<li>Add more pool products in <a href='" . admin_url('edit.php?post_type=pool_product') . "'>Pool Products</a></li>";
echo "<li>Upload images to make the site look even better</li>";
echo "</ul>";

echo "<p><strong>Remember to delete this setup.php file for security!</strong></p>";
?>
