<?php
/**
 * Title: FAQ
 * Slug: twentytwentyfour/text-faq
 * Categories: text, about, featured
 * Keywords: faq, about, frequently asked
 * Viewport width: 1400
 */
?>

<!-- wp:group {"metadata":{"name":"<?php echo esc_html_x( 'FAQs', 'Name of the FAQ pattern', 'twentytwentyfour' ); ?>"},"align":"full","style":{"spacing":{"padding":{"top":"var:preset|spacing|50","bottom":"var:preset|spacing|50","left":"var:preset|spacing|50","right":"var:preset|spacing|50"},"margin":{"top":"0","bottom":"0"}},"elements":{"link":{"color":{"text":"var:preset|color|base"}}}},"backgroundColor":"contrast","textColor":"base","layout":{"type":"constrained"}} -->
<div class="wp-block-group alignfull has-base-color has-contrast-background-color has-text-color has-background has-link-color" style="margin-top:0;margin-bottom:0;padding-top:var(--wp--preset--spacing--50);padding-right:var(--wp--preset--spacing--50);padding-bottom:var(--wp--preset--spacing--50);padding-left:var(--wp--preset--spacing--50)">
	<!-- wp:heading {"align":"wide","style":{"elements":{"link":{"color":{"text":"var:preset|color|base"}}},"typography":{"fontSize":"10rem","letterSpacing":"-0.02em"}},"textColor":"base"} -->
	<h2 class="wp-block-heading alignwide has-base-color has-text-color has-link-color" style="font-size:10rem;letter-spacing:-0.02em"><?php echo esc_html_x( 'FAQs', 'Heading of the FAQs', 'twentytwentyfour' ); ?></h2>
	<!-- /wp:heading -->

	<!-- wp:group {"align":"wide","layout":{"type":"default"}} -->
	<div class="wp-block-group alignwide">
		<!-- wp:separator {"backgroundColor":"base","className":"is-style-wide"} -->
		<hr class="wp-block-separator has-text-color has-base-color has-alpha-channel-opacity has-base-background-color has-background is-style-wide"/>
		<!-- /wp:separator -->

		<!-- wp:details {"style":{"spacing":{"margin":{"top":"0"}},"border":{"top":{"width":"0px","style":"none"},"right":{"width":"0px","style":"none"},"bottom":{"color":"var:preset|color|base","style":"solid","width":"1px"},"left":{"width":"0px","style":"none"}}},"className":"is-style-arrow-icon-details","fontSize":"medium"} -->
		<details class="wp-block-details is-style-arrow-icon-details has-medium-font-size" style="border-top-style:none;border-top-width:0px;border-right-style:none;border-right-width:0px;border-bottom-color:var(--wp--preset--color--base);border-bottom-style:solid;border-bottom-width:1px;border-left-style:none;border-left-width:0px;margin-top:0">
			<summary><?php echo esc_html_x( 'What is your process working in smaller projects?', 'Question on the details block', 'twentytwentyfour' ); ?></summary>
			<!-- wp:paragraph {"placeholder":"Type / to add a hidden block","style":{"elements":{"link":{"color":{"text":"var:preset|color|contrast-1"}}}},"textColor":"contrast-1"} -->
			<p class="has-contrast-1-color has-text-color has-link-color"><?php echo esc_html_x( 'Études offers comprehensive consulting, management, design, and research solutions. Our vision is to be at the forefront of architectural innovation, fostering a global community of architects and enthusiasts united by a passion for creating spaces. Every architectural endeavor is an opportunity to shape the future.', 'Hidden answer on the details block', 'twentytwentyfour' ); ?></p>
			<!-- /wp:paragraph -->
		</details>
		<!-- /wp:details -->

		<!-- wp:details {"style":{"spacing":{"margin":{"top":"0"}},"border":{"top":{"width":"0px","style":"none"},"right":{"width":"0px","style":"none"},"bottom":{"color":"var:preset|color|base","style":"solid","width":"1px"},"left":{"width":"0px","style":"none"}}},"className":"is-style-arrow-icon-details","fontSize":"medium"} -->
		<details class="wp-block-details is-style-arrow-icon-details has-medium-font-size" style="border-top-style:none;border-top-width:0px;border-right-style:none;border-right-width:0px;border-bottom-color:var(--wp--preset--color--base);border-bottom-style:solid;border-bottom-width:1px;border-left-style:none;border-left-width:0px;margin-top:0">
			<summary><?php echo esc_html_x( 'Who is behind Études?', 'Question on the details block', 'twentytwentyfour' ); ?></summary>
			<!-- wp:paragraph {"placeholder":"Type / to add a hidden block","style":{"elements":{"link":{"color":{"text":"var:preset|color|contrast-1"}}}},"textColor":"contrast-1"} -->
			<p class="has-contrast-1-color has-text-color has-link-color"><?php echo esc_html_x( 'Études offers comprehensive consulting, management, design, and research solutions. Our vision is to be at the forefront of architectural innovation, fostering a global community of architects and enthusiasts united by a passion for creating spaces. Every architectural endeavor is an opportunity to shape the future.', 'Hidden answer on the details block', 'twentytwentyfour' ); ?></p>
			<!-- /wp:paragraph -->
		</details>
		<!-- /wp:details -->

		<!-- wp:details {"style":{"spacing":{"margin":{"top":"0"}},"border":{"top":{"width":"0px","style":"none"},"right":{"width":"0px","style":"none"},"bottom":{"color":"var:preset|color|base","style":"solid","width":"1px"},"left":{"width":"0px","style":"none"}}},"className":"is-style-arrow-icon-details","fontSize":"medium"} -->
		<details class="wp-block-details is-style-arrow-icon-details has-medium-font-size" style="border-top-style:none;border-top-width:0px;border-right-style:none;border-right-width:0px;border-bottom-color:var(--wp--preset--color--base);border-bottom-style:solid;border-bottom-width:1px;border-left-style:none;border-left-width:0px;margin-top:0">
			<summary><?php echo esc_html_x( 'I\'d like to get to meet fellow architects, how can I do that?', 'Question on the details block', 'twentytwentyfour' ); ?></summary>
			<!-- wp:paragraph {"placeholder":"Type / to add a hidden block","style":{"elements":{"link":{"color":{"text":"var:preset|color|contrast-1"}}}},"textColor":"contrast-1"} -->
			<p class="has-contrast-1-color has-text-color has-link-color"><?php echo esc_html_x( 'Études offers comprehensive consulting, management, design, and research solutions. Our vision is to be at the forefront of architectural innovation, fostering a global community of architects and enthusiasts united by a passion for creating spaces. Every architectural endeavor is an opportunity to shape the future.', 'Hidden answer on the details block', 'twentytwentyfour' ); ?></p>
			<!-- /wp:paragraph -->
		</details>
		<!-- /wp:details -->

		<!-- wp:details {"style":{"spacing":{"margin":{"top":"0"}},"border":{"top":{"width":"0px","style":"none"},"right":{"width":"0px","style":"none"},"bottom":{"color":"var:preset|color|base","style":"solid","width":"1px"},"left":{"width":"0px","style":"none"}}},"className":"is-style-arrow-icon-details","fontSize":"medium"} -->
		<details class="wp-block-details is-style-arrow-icon-details has-medium-font-size" style="border-top-style:none;border-top-width:0px;border-right-style:none;border-right-width:0px;border-bottom-color:var(--wp--preset--color--base);border-bottom-style:solid;border-bottom-width:1px;border-left-style:none;border-left-width:0px;margin-top:0">
			<summary><?php echo esc_html_x( 'Can I apply to be a part of the team or work as a contractor?', 'Question on the details block', 'twentytwentyfour' ); ?></summary>
			<!-- wp:paragraph {"placeholder":"Type / to add a hidden block","style":{"elements":{"link":{"color":{"text":"var:preset|color|contrast-1"}}}},"textColor":"contrast-1"} -->
			<p class="has-contrast-1-color has-text-color has-link-color"><?php echo esc_html_x( 'Études offers comprehensive consulting, management, design, and research solutions. Our vision is to be at the forefront of architectural innovation, fostering a global community of architects and enthusiasts united by a passion for creating spaces. Every architectural endeavor is an opportunity to shape the future.', 'Hidden answer on the details block', 'twentytwentyfour' ); ?></p>
			<!-- /wp:paragraph -->
		</details>
		<!-- /wp:details -->

		<!-- wp:spacer {"height":"var:preset|spacing|10","style":{"spacing":{"margin":{"top":"var:preset|spacing|10","bottom":"0"}}}} -->
		<div style="margin-top:var(--wp--preset--spacing--10);margin-bottom:0;height:var(--wp--preset--spacing--10)" aria-hidden="true" class="wp-block-spacer"></div>
		<!-- /wp:spacer -->
	</div>
	<!-- /wp:group -->
</div>
<!-- /wp:group -->
