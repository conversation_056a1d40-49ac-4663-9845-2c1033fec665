<?php get_header(); ?>

<main class="main-content">
    <div class="container">
        <?php if (have_posts()) : ?>
            <?php while (have_posts()) : the_post(); ?>
                <article id="post-<?php the_ID(); ?>" <?php post_class('single-post'); ?>>
                    <header class="entry-header">
                        <h1 class="entry-title"><?php the_title(); ?></h1>
                        <div class="entry-meta">
                            <span class="posted-on">
                                <time datetime="<?php echo get_the_date('c'); ?>">
                                    <?php echo get_the_date(); ?>
                                </time>
                            </span>
                            <span class="byline">
                                by <?php the_author(); ?>
                            </span>
                        </div>
                    </header>

                    <?php if (has_post_thumbnail()) : ?>
                        <div class="entry-thumbnail">
                            <?php the_post_thumbnail('large'); ?>
                        </div>
                    <?php endif; ?>

                    <div class="entry-content">
                        <?php the_content(); ?>
                    </div>

                    <footer class="entry-footer">
                        <?php
                        $categories = get_the_category();
                        if (!empty($categories)) :
                        ?>
                            <div class="entry-categories">
                                <strong>Categories:</strong>
                                <?php
                                $category_links = array();
                                foreach ($categories as $category) {
                                    $category_links[] = '<a href="' . esc_url(get_category_link($category->term_id)) . '">' . esc_html($category->name) . '</a>';
                                }
                                echo implode(', ', $category_links);
                                ?>
                            </div>
                        <?php endif; ?>

                        <?php
                        $tags = get_the_tags();
                        if (!empty($tags)) :
                        ?>
                            <div class="entry-tags">
                                <strong>Tags:</strong>
                                <?php
                                $tag_links = array();
                                foreach ($tags as $tag) {
                                    $tag_links[] = '<a href="' . esc_url(get_tag_link($tag->term_id)) . '">' . esc_html($tag->name) . '</a>';
                                }
                                echo implode(', ', $tag_links);
                                ?>
                            </div>
                        <?php endif; ?>
                    </footer>
                </article>

                <?php
                // Navigation between posts
                the_post_navigation(array(
                    'prev_text' => '<span class="nav-subtitle">Previous:</span> <span class="nav-title">%title</span>',
                    'next_text' => '<span class="nav-subtitle">Next:</span> <span class="nav-title">%title</span>',
                ));
                ?>

                <?php
                // Comments
                if (comments_open() || get_comments_number()) :
                    comments_template();
                endif;
                ?>

            <?php endwhile; ?>
        <?php else : ?>
            <div class="no-content">
                <h1>Nothing Found</h1>
                <p>It looks like nothing was found at this location. Maybe try a search?</p>
                <?php get_search_form(); ?>
            </div>
        <?php endif; ?>
    </div>
</main>

<?php get_footer(); ?>
