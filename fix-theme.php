<?php
/**
 * <PERSON><PERSON><PERSON> to fix theme issues
 */

// Include WordPress
require_once('wp-config.php');
require_once('wp-load.php');

echo "=== FIXING THEME ISSUES ===\n\n";

// 1. Reactivate the theme
$theme = 'ezpools';
switch_theme($theme);
echo "✅ Reactivated theme: $theme\n";

// 2. Check and create home page if needed
$home_page = get_page_by_path('home');
if (!$home_page) {
    // Create home page
    $home_page_id = wp_insert_post([
        'post_title' => 'Home',
        'post_name' => 'home',
        'post_content' => 'This is the home page content.',
        'post_status' => 'publish',
        'post_type' => 'page'
    ]);
    echo "✅ Created home page with ID: $home_page_id\n";
} else {
    $home_page_id = $home_page->ID;
    echo "✅ Home page exists with ID: $home_page_id\n";
}

// 3. Set front page settings
update_option('show_on_front', 'page');
update_option('page_on_front', $home_page_id);
echo "✅ Set front page to static page\n";

// 4. Clear all caches
wp_cache_flush();
delete_option('_transient_timeout_theme_roots');
delete_option('_transient_theme_roots');
echo "✅ Cleared caches\n";

// 5. Force theme refresh
update_option('stylesheet', $theme);
update_option('template', $theme);
echo "✅ Forced theme refresh\n";

// 6. Verify settings
echo "\n=== VERIFICATION ===\n";
echo "Current theme: " . wp_get_theme()->get('Name') . "\n";
echo "Show on front: " . get_option('show_on_front') . "\n";
echo "Page on front: " . get_option('page_on_front') . "\n";

// 7. Test if we're on front page
$is_front = is_front_page();
echo "Is front page: " . ($is_front ? 'Yes' : 'No') . "\n";

echo "\n🎉 Theme fixes applied! Please refresh your browser with Ctrl+F5\n";
echo "If issues persist, try visiting: http://localhost:8080/?p=$home_page_id\n";
?>
