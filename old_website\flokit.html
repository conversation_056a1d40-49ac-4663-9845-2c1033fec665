<!DOCTYPE html><html lang="en-US"><head><title>EZ Pool FloKit and Assembly</title><link rel="shortcut icon" href="/favicon.ico" /><meta http-equiv="Content-type" content="text/html;charset=UTF-8" /><meta name="viewport" content="width=300" /><meta name="description" content="We also use quality American made equipment with our pools to make sure the quality is consistent. Call 855-439-7665." /><meta property="og:description" content="We also use quality American made equipment with our pools to make sure the quality is consistent. Call 855-439-7665." /><meta name="generator" content="EverWeb 4.0.1 (2884)" /><meta name="buildDate" content="Thursday, June 19, 2025" /><meta property="og:url" content="https://4ezpool.com/flokit.html" /><meta property="og:title" content="EZ Pool FloKit and Assembly" /><meta property="og:type" content="website" /><link rel="stylesheet" type="text/css" href="ew_css/textstyles.css?3833225815" /><link rel="stylesheet" type="text/css" href="ew_css/responsive.css?3833225815" /><link rel="stylesheet" type="text/css" href="ew_css/globaltextstyles.css?3833225815" /><script type="text/javascript" src="ew_js/imageCode.js" defer></script><script type="text/javascript">
		<!--
		function getContinueShoppingURL(form){
			form.shopping_url.value = window.location.href;
		}
		//-->
</script><script language="JavaScript">

<!--

function MM_swapImgRestore() { //v3.0

  var i,x,a=document.MM_sr; for(i=0;a&&i<a.length&&(x=a[i])&&x.oSrc;i++) x.src=x.oSrc;

}



function MM_preloadImages() { //v3.0

  var d=document; if(d.images){ if(!d.MM_p) d.MM_p=new Array();

    var i,j=d.MM_p.length,a=MM_preloadImages.arguments; for(i=0; i<a.length; i++)

    if (a[i].indexOf("#")!=0){ d.MM_p[j]=new Image; d.MM_p[j++].src=a[i];}}

}



function MM_findObj(n, d) { //v4.0

  var p,i,x;  if(!d) d=document; if((p=n.indexOf("?"))>0&&parent.frames.length) {

    d=parent.frames[n.substring(p+1)].document; n=n.substring(0,p);}

  if(!(x=d[n])&&d.all) x=d.all[n]; for (i=0;!x&&i<d.forms.length;i++) x=d.forms[i][n];

  for(i=0;!x&&d.layers&&i<d.layers.length;i++) x=MM_findObj(n,d.layers[i].document);

  if(!x && document.getElementById) x=document.getElementById(n); return x;

}



function MM_swapImage() { //v3.0

  var i,j=0,x,a=MM_swapImage.arguments; document.MM_sr=new Array; for(i=0;i<(a.length-2);i+=3)

   if ((x=MM_findObj(a[i]))!=null){document.MM_sr[j++]=x; if(!x.oSrc) x.oSrc=x.src; x.src=a[i+2];}

}


////////////////////////////////////////////////////////////////

    // make new window function

    ////////////////////////////////////////////////////////////////

    function createNewWin(newURL)

    {

        var newWindow;



        newWindow = window.open(newURL,"","scrollbars=1,resizable=1,height=400,width=400");

        

    }

	//  -->

  </script><style type="text/css">a img {border:0px;}body {background-color: #FFFEFE;margin: 0px auto;}div.container {margin: 0px auto;width: 300px;height: 695px;}form.ppOrderForm_4 {color:#000000;font-family:Helvetica;font-size:12px;text-align:Left }</style></head><body><div class="container" style="height:695px"><div class="content" data-minheight="300"><div style="position:relative"><div class="shape_0" style="left:19px;top:31px;width:245px;height:71px;z-index:0;position: absolute;"><div class="paraWrap" style="padding: 0px 2.16px 0px 2.16px; "><p class="para52"><span style="line-height: 13.2px;" class="Style219">EZ Pool Plumbing</span></p><p class="para131"><span style="line-height: 19.8px;" class="Style220">Flo-Kit</span></p><p class="para52"><span style="line-height: 15.4px;" class="Style229">Converts pool openings for plumbing.</span></p></div></div></div><div style="position:relative"><div class="shape_1" style="left:0px;top:107px;width:299px;height:224px;z-index:1;position: absolute;"><img src="images/flokit/FloKit.jpg" height="224" width="299" alt="FloKit" data-src2x="images/flokit/<EMAIL>" srcset="images/flokit/FloKit.jpg 1x, images/flokit/<EMAIL> 2x" /></div></div><div style="position:relative"><div class="shape_2" style="left:0px;top:332px;width:300px;height:156px;z-index:2;position: absolute;"><img src="images/flokit/plumbing.png" height="156" width="300" alt="FloKit Assembly" data-src2x="images/flokit/<EMAIL>" srcset="images/flokit/plumbing.png 1x, images/flokit/<EMAIL> 2x" /></div></div><div style="position:relative"><div class="shape_3" style="left:0px;top:494px;width:299px;height:151px;z-index:3;position: absolute;"><p class="para4"><span style="line-height: 21.74584px;" class="Style5">EZ Pools uses commercial grade quality parts with all of their pools. These US made parts are part of the quality experience you get from EZ Pools. Kit includes:</span></p><p class="para4"><span style="line-height: 21.74584px;" class="Style5">Two (2) Gate Valves, Two (2) Wall Fittings, and One (1) Flow Director.</span></p></div></div><div style="position:relative"><div style="left:91.5px;top:645px;height:50px;width:107px;position: absolute;z-index: 4;" id="custom-background-4"><!--107-->
<div style="width:100%;text-align:Left">
<img src="" style="width:107px;height:auto">


<form class="ew_pp ppOrderForm_4" id="ppOrderForm_4" action="https://www.paypal.com/cgi-bin/webscr" method="post" target="paypal">

<input type="hidden" name="business" value="<EMAIL>">

<input type="hidden" name="currency_code" value="USD">
<input type="hidden" name="button_subtype" value="Buy Now">
<input type="hidden" name="no_note" value="0">




<input type="hidden" name="shopping_url" value="#">




Quantity:<br /><input type="text" name="quantity" size="6" value=""><br /><br />


<input type="hidden" name="tax_rate" value="">
<input type="hidden" name="shipping" value="10"><input type="hidden" name="shipping2" value="">
<input type="hidden" name="handling" value="">
<input type="hidden" name="item_name" value="EZ FloKit®">
<input type="hidden" name="item_number" value="ezflokit">
<input type="hidden" name="amount" value="65">


<input type="hidden" name="discount_amount" value="0">





<input type="hidden" name="weight" value="">
<input type="hidden" name="weight_unit" value="lbs">
<input type="hidden" name="cmd" value="_xclick">
<input type="hidden" name="bn" value="RageSoftware_SP">

<input onclick=getContinueShoppingURL(this.form) type="image" src="https://www.paypalobjects.com/webstatic/en_US/i/btn/png/btn_buynow_107x26.png" name="submit" alt="Buy Now"/>

<img alt="" border="0" src="https://www.paypalobjects.com/en_US/i/scr/pixel.gif" width="1" height="1">







</form>
</div></div></div></div><footer data-top='695' data-height='0'></footer></div></body></html>