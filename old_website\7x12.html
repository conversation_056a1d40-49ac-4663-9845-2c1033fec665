<!DOCTYPE html><html lang="en-US"><head><title>7' x 12' EZ Pool - The Better Portable Lap Pool</title><link rel="shortcut icon" href="/favicon.ico" /><meta http-equiv="Content-type" content="text/html;charset=UTF-8" /><meta name="viewport" content="width=1000" /><meta name="description" content="7&amp;#39; x 12&amp;rsquo; EZ Pool - The Better Portable Lap Pool Call 855-439-7665." /><meta property="og:description" content="7&#39; x 12&rsquo; EZ Pool - The Better Portable Lap Pool Call 855-439-7665." /><meta name="generator" content="EverWeb 4.0.1 (2884)" /><meta name="buildDate" content="Thursday, June 19, 2025" /><meta property="og:url" content="https://4ezpool.com/7x12.html" /><meta property="og:title" content="7&#39; x 12&#39; EZ Pool - The Better Portable Lap Pool" /><meta property="og:type" content="website" /><link rel="stylesheet" type="text/css" href="ew_css/textstyles.css?3833225815" /><link rel="stylesheet" type="text/css" href="ew_css/responsive.css?3833225815" /><link rel="stylesheet" type="text/css" href="ew_css/globaltextstyles.css?3833225815" /><script src="//ajax.googleapis.com/ajax/libs/jquery/2.1.3/jquery.min.js"></script>
<script type="text/javascript">
if (typeof jQuery == 'undefined')
{
 document.write(unescape("%3Cscript src='ew_js/jquery.min.js' type='text/javascript'%3E%3C/script%3E"));
}
</script><script type="text/javascript" src="ew_js/imageCode.js" defer></script><script type="text/javascript" src="ew_js/bigpicturejs.js" defer></script><script type="text/javascript" src="ew_js/bigpicturecustomjs.js" defer></script><script type="text/javascript">
		<!--
		function getContinueShoppingURL(form){
			form.shopping_url.value = window.location.href;
		}
		//-->
</script><script language="JavaScript">

<!--

function MM_swapImgRestore() { //v3.0

  var i,x,a=document.MM_sr; for(i=0;a&&i<a.length&&(x=a[i])&&x.oSrc;i++) x.src=x.oSrc;

}



function MM_preloadImages() { //v3.0

  var d=document; if(d.images){ if(!d.MM_p) d.MM_p=new Array();

    var i,j=d.MM_p.length,a=MM_preloadImages.arguments; for(i=0; i<a.length; i++)

    if (a[i].indexOf("#")!=0){ d.MM_p[j]=new Image; d.MM_p[j++].src=a[i];}}

}



function MM_findObj(n, d) { //v4.0

  var p,i,x;  if(!d) d=document; if((p=n.indexOf("?"))>0&&parent.frames.length) {

    d=parent.frames[n.substring(p+1)].document; n=n.substring(0,p);}

  if(!(x=d[n])&&d.all) x=d.all[n]; for (i=0;!x&&i<d.forms.length;i++) x=d.forms[i][n];

  for(i=0;!x&&d.layers&&i<d.layers.length;i++) x=MM_findObj(n,d.layers[i].document);

  if(!x && document.getElementById) x=document.getElementById(n); return x;

}



function MM_swapImage() { //v3.0

  var i,j=0,x,a=MM_swapImage.arguments; document.MM_sr=new Array; for(i=0;i<(a.length-2);i+=3)

   if ((x=MM_findObj(a[i]))!=null){document.MM_sr[j++]=x; if(!x.oSrc) x.oSrc=x.src; x.src=a[i+2];}

}


////////////////////////////////////////////////////////////////

    // make new window function

    ////////////////////////////////////////////////////////////////

    function createNewWin(newURL)

    {

        var newWindow;



        newWindow = window.open(newURL,"","scrollbars=1,resizable=1,height=400,width=400");

        

    }

	//  -->

  </script>
<script
    src="https://www.paypal.com/sdk/js?client-id=AX9N4ry3WhYnKptAbFjycpxoktGYpC-Vd8bfqEerh9_bzuuh7gHofDraddF_PHIeq2yBqjQfy6CUt000&components=messages"
    data-namespace="PayPalSDK">
</script><style type="text/css">a img {border:0px;}body {background-color: #FFFEFE;margin: 0px auto;}div.container {margin: 0px auto;width: 1000px;height: 2313px;}.shape_0 {background: url('images/7x12/shape_0.png') no-repeat;}.shape_1 {background-color:#FFFEFE;opacity: 1.00; filter:alpha(opacity=100);border: 1px solid #000000;border-radius: 11px;}.shape_3 {background: url('images/7x12/shape_3.png') no-repeat;}.shape_4 {background-color:#FFFEFE;opacity: 1.00; filter:alpha(opacity=100);border: 1px solid #000000;border-radius: 11px;}form.ppOrderForm_5 {color:#000000;font-family:Helvetica;font-size:12px;text-align:Center }
	.shape_7 {background-color:#FFFEFE;opacity: 1.00; filter:alpha(opacity=100);border: 1px solid #000000;border-radius: 11px;}form.ppOrderForm_8 {color:#000000;font-family:Helvetica;font-size:12px;text-align:Center }
	.shape_11 {background-color:#FFFEFE;opacity: 1.00; filter:alpha(opacity=100);border: 1px solid #000000;border-radius: 11px;}.shape_14 {background-color:#FFFEFE;opacity: 1.00; filter:alpha(opacity=100);border: 1px solid #000000;border-radius: 11px;}form.ppOrderForm_15 {color:#000000;font-family:Helvetica;font-size:12px;text-align:Center }
	.shape_18 {background-color:#FFFEFE;opacity: 1.00; filter:alpha(opacity=100);border: 1px solid #000000;border-radius: 11px;}.shape_20 {background-color:#FFFEFE;opacity: 1.00; filter:alpha(opacity=100);border: 1px solid #000000;border-radius: 11px;}form.ppOrderForm_21 {color:#000000;font-family:Helvetica;font-size:12px;text-align:Center }
	.shape_24 {background-color:#FFFEFE;opacity: 1.00; filter:alpha(opacity=100);border: 1px solid #000000;border-radius: 11px;}form.ppOrderForm_25 {color:#000000;font-family:Helvetica;font-size:12px;text-align:Center }
	.shape_28 {background-color:#FFFEFE;opacity: 1.00; filter:alpha(opacity=100);border: 1px solid #000000;border-radius: 11px;}form.ppOrderForm_29 {color:#000000;font-family:Helvetica;font-size:12px;text-align:Center }
	.shape_31 {background-color:#FFFEFE;opacity: 1.00; filter:alpha(opacity=100);border: 1px solid #000000;border-radius: 11px;}form.ppOrderForm_32 {color:#000000;font-family:Helvetica;font-size:12px;text-align:Center }
	.shape_35 {background-color:#FFFEFE;opacity: 1.00; filter:alpha(opacity=100);border: 1px solid #000000;border-radius: 11px;}form.ppOrderForm_36 {color:#000000;font-family:Helvetica;font-size:12px;text-align:Center }
	.shape_47 {background-color:#FFFEFE;opacity: 1.00; filter:alpha(opacity=100);border: 1px solid #000000;border-radius: 11px;}form.ppOrderForm_51 {color:#000000;font-family:Helvetica;font-size:12px;text-align:Center }
	.shape_52 {border-top: 3px solid #000000;}.shape_53 {background-color:#FFFEFE;opacity: 1.00; filter:alpha(opacity=100);}form.ppOrderForm_55 {color:#000000;font-family:Helvetica;font-size:12px;text-align:Center }
	form.ppOrderForm_58 {color:#000000;font-family:Helvetica;font-size:12px;text-align:Center }
	form.ppOrderForm_61 {color:#000000;font-family:Helvetica;font-size:12px;text-align:Center }
	.shape_62 {border-top: 1px dashed #68BCFE;}.shape_63 {border-top: 1px dashed #68BCFE;}.shape_66 {background-color:#FFFEFE;opacity: 1.00; filter:alpha(opacity=100);border: 1px solid #000000;border-radius: 11px;}form.ppOrderForm_72 {color:#000000;font-family:Helvetica;font-size:12px;text-align:Center }
	.shape_73 {background-color:#FFFEFE;opacity: 1.00; filter:alpha(opacity=100);border: 1px solid #000000;border-radius: 11px;}form.ppOrderForm_74 {color:#000000;font-family:Helvetica;font-size:12px;text-align:Center }
	form.ppOrderForm_78 {color:#000000;font-family:Helvetica;font-size:12px;text-align:Left }
	#navmenu_menu0 {clear:both;float:left;margin:0;padding:0;width:100%;font-family:'TektonPro-Bold','Arial';font-weight: 700;font-size:18px;z-index:1000;}#navmenu_menu0 ul {margin:0;padding:0;list-style:none;position:relative;right:50%;float:right;}#navmenu_menu0 ul li {margin:0 10px 0 0;padding:0;float:left;position:relative;left:50%;min-width: 80px;}#navmenu_menu0 ul li a {display:block;margin:0;padding:0px;height: 21px;line-height: 21px;text-decoration:underline;color:#007AD9;font-weight: 700;text-align: center;}#navmenu_menu0 ul li a:visited {text-decoration:underline;color:#FE9E3D;}#navmenu_menu0 ul li a:hover {text-decoration:underline;color:#00B000;}#navmenu_menu0 ul li:hover a,#navmenu_menu0 ul li.hover a {text-decoration:underline;color:#00B000;}#navmenu_menu0 ul ul {z-index:11080;display:none;position:absolute;left:0;float:left;right:auto;padding-top:2px;}#navmenu_menu0 ul ul li {left:auto;margin:0;clear:left;float:left;width:100%;}#navmenu_menu0 ul ul li a,#navmenu_menu0 ul li.active li a,#navmenu_menu0 ul li:hover ul li a,#navmenu_menu0 ul li.hover ul li a {background:#CBCBCB;text-decoration:underline;color:#007AD9;padding-top:0px;padding-bottom:0px;border-bottom:1px solid #000000;float:left;width:80px;width:100%;text-align: left;white-space:nowrap;}#navmenu_menu0 ul ul li a:hover,#navmenu_menu0 ul li.active ul li a:hover,#navmenu_menu0 ul li:hover ul li a:hover,#navmenu_menu0 ul li.hover ul li a:hover {background:#CBCBCB;text-decoration:underline;color:#00B000;float:left;}#navmenu_menu0 ul li.last ul {left:auto; right:0;}#navmenu_menu0 ul li.last ul li {float:right;position:relative;right:0px;}#navmenu_menu0 ul li:hover ul,#navmenu_menu0 ul li.hover ul {display:block;}.shape_85 {background-color:#FAFEF9;opacity: 1.00; filter:alpha(opacity=100);border: 2px solid #000000;}.shape_87 {background: url('images/7x12/shape_87.png') no-repeat;}form.ppOrderForm_93 {color:#000000;font-family:Helvetica;font-size:12px;text-align:Center }
	form.ppOrderForm_95 {color:#000000;font-family:Helvetica;font-size:12px;text-align:Center }
	.shape_96 {border-top: 1px dashed #68BCFE;}form.ppOrderForm_100 {color:#000000;font-family:Helvetica;font-size:12px;text-align:Center }
	.shape_101 {border-top: 1px dashed #68BCFE;}.shape_106 {background-color:#FFFEFE;opacity: 1.00; filter:alpha(opacity=100);}form.ppOrderForm_107 {color:#000000;font-family:Helvetica;font-size:12px;text-align:Center }
	@media only screen and (-moz-min-device-pixel-ratio: 1.5), only screen and (-o-min-device-pixel-ratio: 3/2), only screen and (-webkit-min-device-pixel-ratio: 1.5), only screen and (min-devicepixel-ratio: 1.5), only screen and (min-resolution: 1.5dppx) {.shape_0{background: url('images/7x12/<EMAIL>') no-repeat;background-size: 898px 1125px;}.shape_3{background: url('images/7x12/<EMAIL>') no-repeat;background-size: 898px 554px;}.shape_87{background: url('images/7x12/<EMAIL>') no-repeat;background-size: 499px 31px;}}</style></head><body><div class="container" style="height:2313px"><header><div style="position:relative"><a href="contact-us.html?iframe=true&width=600&height=900" data-linkuuid="D421732F30D942A9BAC71356999047EC" rel="ewpopup" class="outerlink"><div class="shape_84" style="left:285px;top:1px;width:400px;height:97px;z-index:84;position: absolute;"><img src="images/7x12/Logo.png" height="97" width="400" data-src2x="images/7x12/<EMAIL>" srcset="images/7x12/Logo.png 1x, images/7x12/<EMAIL> 2x" /></div></a></div></header><div class="content" data-minheight="100"><div style="position:relative"><div class="shape_0" style="left:48.5px;top:272.5px;width:898px;height:1125px;z-index:0;position: absolute;"></div></div><div style="position:relative"><div class="shape_1" style="left:71.5px;top:611.5px;width:849px;height:760px;width:847px;height:758px;z-index:1;position: absolute;"></div></div><div style="position:relative"><a href="https://www.ezpools.org/Album/Don_Boyer_Family.html" target="_blank" class="outerlink"><div class="shape_2" style="left:362px;top:1420px;width:400px;height:84px;z-index:2;position: absolute;"><img src="images/7x12/EZPoolCustomers2.png" height="84" width="400" data-src2x="images/7x12/<EMAIL>" srcset="images/7x12/EZPoolCustomers2.png 1x, images/7x12/<EMAIL> 2x" /></div></a></div><div style="position:relative"><div class="shape_3" style="left:48.5px;top:1560.5px;width:898px;height:554px;z-index:3;position: absolute;"></div></div><div style="position:relative"><div class="shape_4" style="left:698.5px;top:1610.5px;width:228px;height:137px;width:226px;height:135px;z-index:4;position: absolute;"></div></div><div style="position:relative"><div style="left:761px;top:1734px;height:50px;width:107px;position: absolute;z-index: 5;" id="custom-background-5"><!--107-->
<div style="width:100%;text-align:Center">



<form class="ew_pp ppOrderForm_5" id="ppOrderForm_5" action="https://www.paypal.com/cgi-bin/webscr" method="post" target="paypal">

<input type="hidden" name="business" value="<EMAIL>">

<input type="hidden" name="currency_code" value="USD">
<input type="hidden" name="button_subtype" value="product">
<input type="hidden" name="no_note" value="0">
<input type="hidden" name="no_shipping" value="2">



<input type="hidden" name="shopping_url" value="#">











<input type="hidden" name="tax_rate" value="">
<input type="hidden" name="shipping" value=""><input type="hidden" name="shipping2" value="">
<input type="hidden" name="handling_cart" value="">
<input type="hidden" name="item_name" value="Extra 100 Sq Ft Cartridge">
<input type="hidden" name="item_number" value="100Element">
<input type="hidden" name="amount" value="100">


<input type="hidden" name="discount_amount" value="0">





<input type="hidden" name="add" value="1">
<input type="hidden" name="weight" value="5">
<input type="hidden" name="weight_unit" value="lbs">
<input type="hidden" name="cmd" value="_cart">
<input type="hidden" name="bn" value="RageSoftware_SP">


<input onclick=getContinueShoppingURL(this.form) type="image" src="https://www.paypalobjects.com/webstatic/en_US/i/btn/png/btn_addtocart_120x26.png" border="0" name="submit" alt="Add To Cart">

<img alt="" border="0" src="https://www.paypalobjects.com/en_US/i/scr/pixel.gif" width="1" height="1">



</form>
</div></div></div><div style="position:relative"><div class="shape_6" style="left:717px;top:1629px;width:199px;height:22px;z-index:6;position: absolute;"><div class="paraWrap" style="padding: 0px 2.16px 0px 2.16px; "><p class="para27"><span style="line-height: 11.8px;" class="Style141">Extra Cartridge </span><span style="line-height: 7.67px;" class="Style142">(for 100sf)</span></p></div></div></div><div style="position:relative"><div class="shape_7" style="left:395.5px;top:1795.5px;width:135px;height:110px;width:133px;height:108px;z-index:7;position: absolute;"></div></div><div style="position:relative"><div style="left:404px;top:1891px;height:26px;width:107px;position: absolute;z-index: 8;" id="custom-background-8"><!--107-->
<div style="width:100%;text-align:Center">



<form class="ew_pp ppOrderForm_8" id="ppOrderForm_8" action="https://www.paypal.com/cgi-bin/webscr" method="post" target="paypal">

<input type="hidden" name="business" value="<EMAIL>">

<input type="hidden" name="currency_code" value="USD">
<input type="hidden" name="button_subtype" value="product">
<input type="hidden" name="no_note" value="0">
<input type="hidden" name="no_shipping" value="2">



<input type="hidden" name="shopping_url" value="#">











<input type="hidden" name="tax_rate" value="">
<input type="hidden" name="shipping" value=""><input type="hidden" name="shipping2" value="">
<input type="hidden" name="handling_cart" value="">
<input type="hidden" name="item_name" value="4-Way Outdoor Timer">
<input type="hidden" name="item_number" value="timer">
<input type="hidden" name="amount" value="45">


<input type="hidden" name="discount_amount" value="0">





<input type="hidden" name="add" value="1">
<input type="hidden" name="weight" value="1">
<input type="hidden" name="weight_unit" value="lbs">
<input type="hidden" name="cmd" value="_cart">
<input type="hidden" name="bn" value="RageSoftware_SP">


<input onclick=getContinueShoppingURL(this.form) type="image" src="https://www.paypalobjects.com/webstatic/en_US/i/btn/png/btn_addtocart_120x26.png" border="0" name="submit" alt="Add To Cart">

<img alt="" border="0" src="https://www.paypalobjects.com/en_US/i/scr/pixel.gif" width="1" height="1">



</form>
</div></div></div><div style="position:relative"><div class="shape_9" style="left:404px;top:1805px;width:116px;height:45px;z-index:9;position: absolute;"><div class="paraWrap" style="padding: 0px 2.16px 0px 2.16px; "><p class="para27"><span style="line-height: 11.8px;" class="Style128">Timer</span></p><p class="para27"><span style="line-height: 11.8px;" class="Style128">Add:</span><span style="line-height: 11.8px;" class="Style143"> $45.00</span></p></div></div></div><div style="position:relative"><div class="shape_10" style="left:418px;top:1856px;width:93px;height:35px;z-index:10;position: absolute;"><div class="paraWrap" style="padding: 1px 2.16px 0px 2.16px; "><p class="para131"><span style="line-height: 12px;" class="Style144">4-Way</span></p><p class="para131"><span style="line-height: 12px;" class="Style144">Outdoor Timer</span></p></div></div></div><div style="position:relative"><div class="shape_11" style="left:64.5px;top:1795.5px;width:145px;height:109px;width:143px;height:107px;z-index:11;position: absolute;"></div></div><div style="position:relative"><div class="shape_12" style="left:65px;top:1804px;width:144px;height:45px;z-index:12;position: absolute;"><div class="paraWrap" style="padding: 0px 2.16px 0px 2.16px; "><p class="para27"><span style="line-height: 11.8px;" class="Style128">A-Frame Ladder</span></p><p class="para27"><span style="line-height: 11.8px;" class="Style128">Add:</span><span style="line-height: 11.8px;" class="Style143">&nbsp;&nbsp;$400.00</span></p></div></div></div><div style="position:relative"><a href="aframe-ladder.html?iframe=true&width=300&height=300" data-linkuuid="BADF170625D043FDAE9C1C2412CE79B7" rel="ewpopup" class="outerlink"><div class="shape_13" style="left:77px;top:1854px;width:120px;height:28px;z-index:13;position: absolute;"><div class="paraWrap" style="padding: 0px 2.16px 0px 2.16px; "><p class="para52"><span style="line-height: 13px;" class="linkStyle_145"><a href="aframe-ladder.html?iframe=true&width=600&height=600" data-linkuuid="BADF170625D043FDAE9C1C2412CE79B7" rel="ewpopup"  class="linkStyle_145">Click Here</a></span></p><p class="para52"><span style="line-height: 13px;" class="linkStyle_145"><a href="aframe-ladder.html?iframe=true&width=600&height=600" data-linkuuid="BADF170625D043FDAE9C1C2412CE79B7" rel="ewpopup"  class="linkStyle_145">for More Info:</a></span></p></div></div></a></div><div style="position:relative"><div class="shape_14" style="left:228.5px;top:1795.5px;width:145px;height:109px;width:143px;height:107px;z-index:14;position: absolute;"></div></div><div style="position:relative"><div style="left:241px;top:1890px;height:27px;width:107px;position: absolute;z-index: 15;" id="custom-background-15"><!--107-->
<div style="width:100%;text-align:Center">



<form class="ew_pp ppOrderForm_15" id="ppOrderForm_15" action="https://www.paypal.com/cgi-bin/webscr" method="post" target="paypal">

<input type="hidden" name="business" value="<EMAIL>">

<input type="hidden" name="currency_code" value="USD">
<input type="hidden" name="button_subtype" value="product">
<input type="hidden" name="no_note" value="0">
<input type="hidden" name="no_shipping" value="2">



<input type="hidden" name="shopping_url" value="#">











<input type="hidden" name="tax_rate" value="">
<input type="hidden" name="shipping" value="35"><input type="hidden" name="shipping2" value="">
<input type="hidden" name="handling_cart" value="">
<input type="hidden" name="item_name" value="Pool Step">
<input type="hidden" name="item_number" value="Step">
<input type="hidden" name="amount" value="500">


<input type="hidden" name="discount_amount" value="0">





<input type="hidden" name="add" value="1">
<input type="hidden" name="weight" value="25">
<input type="hidden" name="weight_unit" value="lbs">
<input type="hidden" name="cmd" value="_cart">
<input type="hidden" name="bn" value="RageSoftware_SP">


<input onclick=getContinueShoppingURL(this.form) type="image" src="https://www.paypalobjects.com/webstatic/en_US/i/btn/png/btn_addtocart_120x26.png" border="0" name="submit" alt="Add To Cart">

<img alt="" border="0" src="https://www.paypalobjects.com/en_US/i/scr/pixel.gif" width="1" height="1">



</form>
</div></div></div><div style="position:relative"><div class="shape_16" style="left:229px;top:1804px;width:144px;height:45px;z-index:16;position: absolute;"><div class="paraWrap" style="padding: 0px 2.16px 0px 2.16px; "><p class="para27"><span style="line-height: 11.8px;" class="Style128">Inside Step</span></p><p class="para27"><span style="line-height: 11.8px;" class="Style128">Add:</span><span style="line-height: 11.8px;" class="Style143"> $500.0</span></p></div></div></div><div style="position:relative"><a href="step.html?iframe=true&width=300&height=300" data-linkuuid="040BF41DF2564D07A230F2E0DF012A0E" rel="ewpopup" class="outerlink"><div class="shape_17" style="left:241px;top:1854px;width:120px;height:28px;z-index:17;position: absolute;"><div class="paraWrap" style="padding: 0px 2.16px 0px 2.16px; "><p class="para52"><span style="line-height: 13px;" class="linkStyle_145"><a href="step.html?iframe=true&width=600&height=600" data-linkuuid="040BF41DF2564D07A230F2E0DF012A0E" rel="ewpopup"  class="linkStyle_145">Click Here</a></span></p><p class="para52"><span style="line-height: 13px;" class="linkStyle_145"><a href="step.html?iframe=true&width=600&height=600" data-linkuuid="040BF41DF2564D07A230F2E0DF012A0E" rel="ewpopup"  class="linkStyle_145">for More Info:</a></span></p></div></div></a></div><div style="position:relative"><div class="shape_18" style="left:63.5px;top:1947.5px;width:203px;height:143px;width:201px;height:141px;z-index:18;position: absolute;"></div></div><div style="position:relative"><div class="shape_19" style="left:67px;top:1954px;width:193px;height:48px;z-index:19;position: absolute;"><div class="paraWrap" style="padding: 0px 2.16px 0px 2.16px; "><p class="para27"><span style="line-height: 11.8px;" class="Style128">In-Pool Skimmer</span></p><p class="para27"><span style="line-height: 11.8px;" class="Style128">Add:</span><span style="line-height: 11.8px;" class="Style143"> $175.00</span></p></div></div></div><div style="position:relative"><div class="shape_20" style="left:550.5px;top:1795.5px;width:212px;height:111px;width:210px;height:109px;z-index:20;position: absolute;"></div></div><div style="position:relative"><div style="left:605px;top:1890px;height:27px;width:107px;position: absolute;z-index: 21;" id="custom-background-21"><!--107-->
<div style="width:100%;text-align:Center">



<form class="ew_pp ppOrderForm_21" id="ppOrderForm_21" action="https://www.paypal.com/cgi-bin/webscr" method="post" target="paypal">

<input type="hidden" name="business" value="<EMAIL>">

<input type="hidden" name="currency_code" value="USD">
<input type="hidden" name="button_subtype" value="product">
<input type="hidden" name="no_note" value="0">
<input type="hidden" name="no_shipping" value="2">



<input type="hidden" name="shopping_url" value="#">











<input type="hidden" name="tax_rate" value="">
<input type="hidden" name="shipping" value="0"><input type="hidden" name="shipping2" value="">
<input type="hidden" name="handling_cart" value="">
<input type="hidden" name="item_name" value="EZ Pool Care Kit">
<input type="hidden" name="item_number" value="carekit">
<input type="hidden" name="amount" value="125">


<input type="hidden" name="discount_amount" value="0">





<input type="hidden" name="add" value="1">
<input type="hidden" name="weight" value="5">
<input type="hidden" name="weight_unit" value="lbs">
<input type="hidden" name="cmd" value="_cart">
<input type="hidden" name="bn" value="RageSoftware_SP">


<input onclick=getContinueShoppingURL(this.form) type="image" src="https://www.paypalobjects.com/webstatic/en_US/i/btn/png/btn_addtocart_120x26.png" border="0" name="submit" alt="Add To Cart">

<img alt="" border="0" src="https://www.paypalobjects.com/en_US/i/scr/pixel.gif" width="1" height="1">



</form>
</div></div></div><div style="position:relative"><div class="shape_22" style="left:551px;top:1805px;width:218px;height:23px;z-index:22;position: absolute;"><div class="paraWrap" style="padding: 0px 2.16px 0px 2.16px; "><p class="para27"><span style="line-height: 11.8px;" class="Style128">Care Kit </span><span style="line-height: 11.8px;" class="Style143"> </span><span style="line-height: 11.8px;" class="Style128">Add:</span><span style="line-height: 11.8px;" class="Style143"> $125</span></p></div></div></div><div style="position:relative"><a href="care-kit.html?iframe=true&width=300&height=300" data-linkuuid="25C5430DA22342ECADCB7213053A7C77" rel="ewpopup" class="outerlink"><div class="shape_23" style="left:557px;top:1871px;width:192px;height:19px;z-index:23;position: absolute;"><div class="paraWrap" style="padding: 0px 2.16px 0px 2.16px; "><p class="para52"><span style="line-height: 13px;" class="linkStyle_145"><a href="care-kit.html?iframe=true&width=300&height=300" data-linkuuid="25C5430DA22342ECADCB7213053A7C77" rel="ewpopup"  class="linkStyle_145">Click Here for More Info:</a></span></p></div></div></a></div><div style="position:relative"><div class="shape_24" style="left:790.5px;top:1795.5px;width:136px;height:111px;width:134px;height:109px;z-index:24;position: absolute;"></div></div><div style="position:relative"><div style="left:799px;top:1893px;height:27px;width:107px;position: absolute;z-index: 25;" id="custom-background-25"><!--107-->
<div style="width:100%;text-align:Center">



<form class="ew_pp ppOrderForm_25" id="ppOrderForm_25" action="https://www.paypal.com/cgi-bin/webscr" method="post" target="paypal">

<input type="hidden" name="business" value="<EMAIL>">

<input type="hidden" name="currency_code" value="USD">
<input type="hidden" name="button_subtype" value="product">
<input type="hidden" name="no_note" value="0">
<input type="hidden" name="no_shipping" value="2">



<input type="hidden" name="shopping_url" value="#">











<input type="hidden" name="tax_rate" value="">
<input type="hidden" name="shipping" value="10"><input type="hidden" name="shipping2" value="">
<input type="hidden" name="handling_cart" value="">
<input type="hidden" name="item_name" value="Extra Flo-Kit">
<input type="hidden" name="item_number" value="flokit">
<input type="hidden" name="amount" value="65">


<input type="hidden" name="discount_amount" value="0">





<input type="hidden" name="add" value="1">
<input type="hidden" name="weight" value="2">
<input type="hidden" name="weight_unit" value="lbs">
<input type="hidden" name="cmd" value="_cart">
<input type="hidden" name="bn" value="RageSoftware_SP">


<input onclick=getContinueShoppingURL(this.form) type="image" src="https://www.paypalobjects.com/webstatic/en_US/i/btn/png/btn_addtocart_120x26.png" border="0" name="submit" alt="Add To Cart">

<img alt="" border="0" src="https://www.paypalobjects.com/en_US/i/scr/pixel.gif" width="1" height="1">



</form>
</div></div></div><div style="position:relative"><div class="shape_26" style="left:791px;top:1800px;width:135px;height:45px;z-index:26;position: absolute;"><div class="paraWrap" style="padding: 0px 2.16px 0px 2.16px; "><p class="para27"><span style="line-height: 11.8px;" class="Style128">Extra Flo-Kit</span></p><p class="para27"><span style="line-height: 11.8px;" class="Style128">Add:</span><span style="line-height: 11.8px;" class="Style143"> $65.00</span></p></div></div></div><div style="position:relative"><a href="flokit.html?iframe=true&width=300&height=300" data-linkuuid="9E5D53EFCA11464EA138EE06B3D2B4E3" rel="ewpopup" class="outerlink"><div class="shape_27" style="left:791px;top:1856px;width:135px;height:27px;z-index:27;position: absolute;"><div class="paraWrap" style="padding: 0px 2.16px 0px 2.16px; "><p class="para52"><span style="line-height: 13px;" class="linkStyle_145"><a href="flokit.html?iframe=true&width=600&height=600" data-linkuuid="9E5D53EFCA11464EA138EE06B3D2B4E3" rel="ewpopup"  class="linkStyle_145">Click Here</a></span></p><p class="para52"><span style="line-height: 13px;" class="linkStyle_145"><a href="flokit.html?iframe=true&width=600&height=600" data-linkuuid="9E5D53EFCA11464EA138EE06B3D2B4E3" rel="ewpopup"  class="linkStyle_145">for More Info:</a></span></p></div></div></a></div><div style="position:relative"><div class="shape_28" style="left:287.5px;top:1946.5px;width:219px;height:127px;width:217px;height:125px;z-index:28;position: absolute;"></div></div><div style="position:relative"><div style="left:347px;top:2057px;height:27px;width:107px;position: absolute;z-index: 29;" id="custom-background-29"><!--107-->
<div style="width:100%;text-align:Center">



<form class="ew_pp ppOrderForm_29" id="ppOrderForm_29" action="https://www.paypal.com/cgi-bin/webscr" method="post" target="paypal">

<input type="hidden" name="business" value="<EMAIL>">

<input type="hidden" name="currency_code" value="USD">
<input type="hidden" name="button_subtype" value="product">
<input type="hidden" name="no_note" value="0">
<input type="hidden" name="no_shipping" value="2">



<input type="hidden" name="shopping_url" value="#">











<input type="hidden" name="tax_rate" value="">
<input type="hidden" name="shipping" value="0"><input type="hidden" name="shipping2" value="">
<input type="hidden" name="handling_cart" value="">
<input type="hidden" name="item_name" value="Solar Cover">
<input type="hidden" name="item_number" value="solar">
<input type="hidden" name="amount" value="65">


<input type="hidden" name="discount_amount" value="0">





<input type="hidden" name="add" value="1">
<input type="hidden" name="weight" value="5">
<input type="hidden" name="weight_unit" value="lbs">
<input type="hidden" name="cmd" value="_cart">
<input type="hidden" name="bn" value="RageSoftware_SP">


<input onclick=getContinueShoppingURL(this.form) type="image" src="https://www.paypalobjects.com/webstatic/en_US/i/btn/png/btn_addtocart_120x26.png" border="0" name="submit" alt="Add To Cart">

<img alt="" border="0" src="https://www.paypalobjects.com/en_US/i/scr/pixel.gif" width="1" height="1">



</form>
</div></div></div><div style="position:relative"><div class="shape_30" style="left:290px;top:1956px;width:216px;height:23px;z-index:30;position: absolute;"><div class="paraWrap" style="padding: 0px 2.16px 0px 2.16px; "><p class="para27"><span style="line-height: 11.8px;" class="Style128">Solar Covers </span><span style="line-height: 11.8px;" class="Style143"> </span><span style="line-height: 11.8px;" class="Style128">Add:</span><span style="line-height: 11.8px;" class="Style143"> $65</span></p></div></div></div><div style="position:relative"><div class="shape_31" style="left:530.5px;top:1947.5px;width:228px;height:127px;width:226px;height:125px;z-index:31;position: absolute;"></div></div><div style="position:relative"><div style="left:583px;top:2057px;height:27px;width:107px;position: absolute;z-index: 32;" id="custom-background-32"><!--107-->
<div style="width:100%;text-align:Center">



<form class="ew_pp ppOrderForm_32" id="ppOrderForm_32" action="https://www.paypal.com/cgi-bin/webscr" method="post" target="paypal">

<input type="hidden" name="business" value="<EMAIL>">

<input type="hidden" name="currency_code" value="USD">
<input type="hidden" name="button_subtype" value="product">
<input type="hidden" name="no_note" value="0">
<input type="hidden" name="no_shipping" value="2">



<input type="hidden" name="shopping_url" value="#">











<input type="hidden" name="tax_rate" value="">
<input type="hidden" name="shipping" value="0"><input type="hidden" name="shipping2" value="">
<input type="hidden" name="handling_cart" value="">
<input type="hidden" name="item_name" value="Pool Cover to Fit Size">
<input type="hidden" name="item_number" value="poolgard">
<input type="hidden" name="amount" value="200">


<input type="hidden" name="discount_amount" value="0">





<input type="hidden" name="add" value="1">
<input type="hidden" name="weight" value="10">
<input type="hidden" name="weight_unit" value="lbs">
<input type="hidden" name="cmd" value="_cart">
<input type="hidden" name="bn" value="RageSoftware_SP">


<input onclick=getContinueShoppingURL(this.form) type="image" src="https://www.paypalobjects.com/webstatic/en_US/i/btn/png/btn_addtocart_120x26.png" border="0" name="submit" alt="Add To Cart">

<img alt="" border="0" src="https://www.paypalobjects.com/en_US/i/scr/pixel.gif" width="1" height="1">



</form>
</div></div></div><div style="position:relative"><div class="shape_33" style="left:531px;top:1957px;width:226px;height:23px;z-index:33;position: absolute;"><div class="paraWrap" style="padding: 0px 2.16px 0px 2.16px; "><p class="para27"><span style="line-height: 11.8px;" class="Style128">Pool Covers Add: $200</span></p></div></div></div><div style="position:relative"><a href="pool-cover.html?iframe=true&width=300&height=300" data-linkuuid="BA3B94D86A1C44629800704956AC1199" rel="ewpopup" class="outerlink"><div class="shape_34" style="left:544px;top:2040px;width:192px;height:19px;z-index:34;position: absolute;"><div class="paraWrap" style="padding: 0px 2.16px 0px 2.16px; "><p class="para52"><span style="line-height: 13px;" class="linkStyle_145"><a href="pool-cover.html?iframe=true&width=300&height=400" data-linkuuid="BA3B94D86A1C44629800704956AC1199" rel="ewpopup"  class="linkStyle_145">Click Here for More Info:</a></span></p></div></div></a></div><div style="position:relative"><div class="shape_35" style="left:790.5px;top:1947.5px;width:136px;height:128px;width:134px;height:126px;z-index:35;position: absolute;"></div></div><div style="position:relative"><div style="left:799px;top:2057px;height:27px;width:107px;position: absolute;z-index: 36;" id="custom-background-36"><!--107-->
<div style="width:100%;text-align:Center">



<form class="ew_pp ppOrderForm_36" id="ppOrderForm_36" action="https://www.paypal.com/cgi-bin/webscr" method="post" target="paypal">

<input type="hidden" name="business" value="<EMAIL>">

<input type="hidden" name="currency_code" value="USD">
<input type="hidden" name="button_subtype" value="product">
<input type="hidden" name="no_note" value="0">
<input type="hidden" name="no_shipping" value="2">



<input type="hidden" name="shopping_url" value="#">











<input type="hidden" name="tax_rate" value="">
<input type="hidden" name="shipping" value="10"><input type="hidden" name="shipping2" value="">
<input type="hidden" name="handling_cart" value="">
<input type="hidden" name="item_name" value="EZ SwimMill The Swimmer's Treadmill">
<input type="hidden" name="item_number" value="swimmill">
<input type="hidden" name="amount" value="50">


<input type="hidden" name="discount_amount" value="0">





<input type="hidden" name="add" value="1">
<input type="hidden" name="weight" value="1">
<input type="hidden" name="weight_unit" value="lbs">
<input type="hidden" name="cmd" value="_cart">
<input type="hidden" name="bn" value="RageSoftware_SP">


<input onclick=getContinueShoppingURL(this.form) type="image" src="https://www.paypalobjects.com/webstatic/en_US/i/btn/png/btn_addtocart_120x26.png" border="0" name="submit" alt="Add To Cart">

<img alt="" border="0" src="https://www.paypalobjects.com/en_US/i/scr/pixel.gif" width="1" height="1">



</form>
</div></div></div><div style="position:relative"><div class="shape_37" style="left:791px;top:1966px;width:135px;height:45px;z-index:37;position: absolute;"><div class="paraWrap" style="padding: 0px 2.16px 0px 2.16px; "><p class="para27"><span style="line-height: 11.8px;" class="Style128">EZ SwimMill</span></p><p class="para27"><span style="line-height: 11.8px;" class="Style128">Add:</span><span style="line-height: 11.8px;" class="Style143"> $50.00</span></p></div></div></div><div style="position:relative"><a href="ez-swimmill.html?iframe=true&width=300&height=300" data-linkuuid="4DD107C9D25348E291C25221FFE6D17B" rel="ewpopup" class="outerlink"><div class="shape_38" style="left:791px;top:2019px;width:135px;height:27px;z-index:38;position: absolute;"><div class="paraWrap" style="padding: 0px 2.16px 0px 2.16px; "><p class="para52"><span style="line-height: 13px;" class="linkStyle_145"><a href="ez-swimmill.html?iframe=true&width=300&height=300" data-linkuuid="4DD107C9D25348E291C25221FFE6D17B" rel="ewpopup"  class="linkStyle_145">Click Here</a></span></p><p class="para52"><span style="line-height: 13px;" class="linkStyle_145"><a href="ez-swimmill.html?iframe=true&width=300&height=300" data-linkuuid="4DD107C9D25348E291C25221FFE6D17B" rel="ewpopup"  class="linkStyle_145">for More Info:</a></span></p></div></div></a></div><div style="position:relative"><div class="shape_39" style="left:713px;top:1657px;width:220px;height:35px;z-index:39;position: absolute;"><div class="paraWrap" style="padding: 0px 2.16px 0px 2.16px; "><p class="para126"><span style="line-height: 12px;" class="Style148">√</span><span style="line-height: 12px;" class="Style132"> Cleanable and Reusable up to 2 Years</span></p><p class="para126"><span style="line-height: 12px;" class="Style148">√</span><span style="line-height: 12px;" class="Style132"> Always Handy to have an Extra One</span></p></div></div></div><div style="position:relative"><div class="shape_40" style="left:563px;top:1833px;width:195px;height:46px;z-index:40;position: absolute;"><div class="paraWrap" style="padding: 0px 2.16px 0px 2.16px; "><p class="para126"><span style="line-height: 12px;" class="Style148">√</span><span style="line-height: 12px;" class="Style132"> Helps to keep pool clean</span></p><p class="para126"><span style="line-height: 12px;" class="Style148">√</span><span style="line-height: 12px;" class="Style132"> Everything needed except Sanitizer</span></p></div></div></div><div style="position:relative"><div class="shape_41" style="left:297px;top:1992px;width:191px;height:52px;z-index:41;position: absolute;"><div class="paraWrap" style="padding: 0px 2.16px 0px 2.16px; "><p class="para126"><span style="line-height: 12px;" class="Style148">√</span><span style="line-height: 12px;" class="Style132"> Helps keep Pool Clean from Debris</span></p><p class="para126"><span style="line-height: 12px;" class="Style148">√</span><span style="line-height: 12px;" class="Style132"> Heats Water During Sunny Days</span></p><p class="para126"><span style="line-height: 12px;" class="Style148">√</span><span style="line-height: 12px;" class="Style132"> Helps to Prevent Heat Loss</span></p></div></div></div><div style="position:relative"><div class="shape_42" style="left:549px;top:1986px;width:195px;height:53px;z-index:42;position: absolute;"><div class="paraWrap" style="padding: 0px 2.16px 0px 2.16px; "><p class="para126"><span style="line-height: 12px;" class="Style148">√</span><span style="line-height: 12px;" class="Style132"> Connects to Pool Corners</span></p><p class="para126"><span style="line-height: 12px;" class="Style148">√</span><span style="line-height: 12px;" class="Style132"> Supports an Adult's Weight </span><span style="line-height: 10px;" class="Style149">(when full)</span></p><p class="para126"><span style="line-height: 12px;" class="Style148">√</span><span style="line-height: 12px;" class="Style132"> Ideal for Winterizing</span></p></div></div></div><div style="position:relative"><div class="shape_43" style="left:223px;top:1433px;width:123px;height:70px;z-index:43;position: absolute;"><div class="paraWrap" style="padding: 0px 2.16px 0px 2.16px; "><p class="para27"><span style="line-height: 20px;" class="linkStyle_150"><a href="http://www.ezpools.org" class="linkStyle_150">Check out our Customer's Pools</a></span></p></div></div></div><div style="position:relative"><div class="shape_44" style="left:624px;top:681px;width:261px;height:48px;z-index:44;position: absolute;"><div class="paraWrap" style="padding: 0px 2.16px 0px 2.16px; "><p class="para27"><span style="line-height: 6.12px;" class="Style153">Pools Made-to-Order</span></p><p class="para27"><span style="line-height: 6.12px;" class="Style153">Ship in 10 -14 Business Days</span></p></div></div></div><div style="position:relative"><div class="shape_45" style="left:784px;top:785px;width:95px;height:22px;z-index:45;position: absolute;"><div class="paraWrap" style="padding: 0px 2.16px 0px 2.16px; "><p class="para27"><span style="line-height: 11.8px;" class="Style128">Cost: $138</span></p></div></div></div><div style="position:relative"><div class="shape_46" style="left:94px;top:630px;width:442px;height:92px;z-index:46;position: absolute;"><div class="paraWrap" style="padding: 0px 2.16px 0px 2.16px; "><p class="para126"><span style="line-height: 19.8px;" class="Style154">7' x 12' EZ Pool Only</span><span style="line-height: 19.8px;" class="Style128">:</span><span style="line-height: 15.84px;" class="Style129">&nbsp;&nbsp; </span><span style="line-height: 21.78px;" class="Style54">EZ Pools are Made-to-Order right here in the USA. They are backed by a </span><span style="line-height: 21.78px;" class="linkStyle_55"><a href="warranty.html?iframe=true&width=600&height=300" data-linkuuid="43D1BF25435448F381F5D8076B17A7B0" rel="ewpopup"  class="linkStyle_55">Five-Year Warranty</a></span><span style="line-height: 21.78px;" class="Style54">, made from only </span><span style="line-height: 21.78px;" class="linkStyle_55"><a href="our-product.html?iframe=true&width=600&height=300" data-linkuuid="55B11428EDDE4AD8B8A6296C174DC01F" rel="ewpopup"  class="linkStyle_55">American-Made Components</a></span><span style="line-height: 21.78px;" class="Style54">, and each pool includes one </span><span style="line-height: 21.78px;" class="linkStyle_55"><a href="flokit.html?iframe=true&width=300&height=300" data-linkuuid="9E5D53EFCA11464EA138EE06B3D2B4E3" rel="ewpopup"  class="linkStyle_55">Flo-Kit Set</a></span><span style="line-height: 21.78px;" class="Style54">.</span></p></div></div></div><div style="position:relative"><div class="shape_47" style="left:470.5px;top:1580.5px;width:211px;height:169px;width:209px;height:167px;z-index:47;position: absolute;"></div></div><div style="position:relative"><div class="shape_48" style="left:471px;top:1605px;width:210px;height:22px;z-index:48;position: absolute;"><div class="paraWrap" style="padding: 0px 2.16px 0px 2.16px; "><p class="para27"><span style="line-height: 11.8px;" class="Style128">2 Speed 100 sq ft P/F</span><span style="line-height: 11.8px;" class="Style143"> </span></p></div></div></div><div style="position:relative"><div class="shape_49" style="left:486px;top:1627px;width:179px;height:74px;z-index:49;position: absolute;"><div class="paraWrap" style="padding: 0px 2.16px 0px 2.16px; "><p class="para126"><span style="line-height: 12px;" class="Style148">√</span><span style="line-height: 12px;" class="Style132"> Meets DOE 2021 Requirements</span></p><p class="para126"><span style="line-height: 12px;" class="Style148">√</span><span style="line-height: 12px;" class="Style132"> Filters upto 75 GPM</span></p><p class="para126"><span style="line-height: 12px;" class="Style148">√</span><span style="line-height: 12px;" class="Style132"> Reusable Polyester Element</span></p><p class="para126"><span style="line-height: 12px;" class="Style148">√</span><span style="line-height: 12px;" class="Style132"> Fits regular 110v Outlet</span></p></div></div></div><div style="position:relative"><div class="shape_50" style="left:495px;top:1698px;width:163px;height:18px;z-index:50;position: absolute;"><div class="paraWrap" style="padding: 0px 2.16px 0px 2.16px; "><p class="para52"><span style="line-height: 13px;" class="linkStyle_145"><a href="15100.html?iframe=true&width=600&height=600" data-linkuuid="8656FF8BD7564231B8E1F09566F6D949" rel="ewpopup"  class="linkStyle_145">Click Here for More Info:</a></span></p></div></div></div><div style="position:relative"><div style="left:519px;top:1737px;height:26px;width:107px;position: absolute;z-index: 51;" id="custom-background-51"><!--107-->
<div style="width:100%;text-align:Center">



<form class="ew_pp ppOrderForm_51" id="ppOrderForm_51" action="https://www.paypal.com/cgi-bin/webscr" method="post" target="paypal">

<input type="hidden" name="business" value="<EMAIL>">

<input type="hidden" name="currency_code" value="USD">
<input type="hidden" name="button_subtype" value="product">
<input type="hidden" name="no_note" value="0">
<input type="hidden" name="no_shipping" value="2">



<input type="hidden" name="shopping_url" value="#">











<input type="hidden" name="tax_rate" value="">
<input type="hidden" name="shipping" value=""><input type="hidden" name="shipping2" value="">
<input type="hidden" name="handling_cart" value="">
<input type="hidden" name="item_name" value="2 Speed 110v Pump 100 Sq Ft Filter">
<input type="hidden" name="item_number" value="2SP100">
<input type="hidden" name="amount" value="1000">


<input type="hidden" name="discount_amount" value="0">





<input type="hidden" name="add" value="1">
<input type="hidden" name="weight" value="20">
<input type="hidden" name="weight_unit" value="lbs">
<input type="hidden" name="cmd" value="_cart">
<input type="hidden" name="bn" value="RageSoftware_SP">


<input onclick=getContinueShoppingURL(this.form) type="image" src="https://www.paypalobjects.com/webstatic/en_US/i/btn/png/btn_addtocart_120x26.png" border="0" name="submit" alt="Add To Cart">

<img alt="" border="0" src="https://www.paypalobjects.com/en_US/i/scr/pixel.gif" width="1" height="1">



</form>
</div></div></div><div style="position:relative"><div class="shape_52" style="left:111.5px;top:738.5px;width:762px;width:762px;margin-top:9.5px;z-index:52;position: absolute;"></div></div><div style="position:relative"><div class="shape_53" style="left:424px;top:735px;width:103px;height:32px;z-index:53;position: absolute;"><div class="paraWrap" style="padding: 0px 2.16px 0px 2.16px; "><p class="para27"><span style="line-height: 22px;" class="Style155">Options</span></p></div></div></div><div style="position:relative"><div class="shape_54" style="left:94px;top:767px;width:654px;height:92px;z-index:54;position: absolute;"><div class="paraWrap" style="padding: 0px 2.16px 0px 2.16px; "><p class="para126"><span style="line-height: 19.8px;" class="Style127">Add RimGard</span><span style="line-height: 19.8px;" class="Style128">:</span><span style="line-height: 15.84px;" class="Style129">&nbsp;&nbsp; </span><span style="line-height: 21.78px;" class="Style54">The top edge of your EZ Pool is the only area of the pool exposed to the sun constantly and therefore can be very hot to touch on hot summer days. The RimGard is a specially treated all white material that is cool to touch and easy to clean. If ordered, this is added to your pool wall rim. </span><span style="line-height: 21.78px;" class="linkStyle_55"><a href="rimgard.html?iframe=true&width=1000&height=800" data-linkuuid="D228CBCF8B0E4A92A00C77F682DBBCF3" rel="ewpopup"  class="linkStyle_55">Learn More &gt;&gt;&gt;</a></span></p></div></div></div><div style="position:relative"><div style="left:767.001px;top:807px;height:26px;width:118px;position: absolute;z-index: 55;" id="custom-background-55"><!--118-->
<div style="width:100%;text-align:Center">



<form class="ew_pp ppOrderForm_55" id="ppOrderForm_55" action="https://www.paypal.com/cgi-bin/webscr" method="post" target="paypal">

<input type="hidden" name="business" value="<EMAIL>">

<input type="hidden" name="currency_code" value="USD">
<input type="hidden" name="button_subtype" value="product">
<input type="hidden" name="no_note" value="0">
<input type="hidden" name="no_shipping" value="2">



<input type="hidden" name="shopping_url" value="#">











<input type="hidden" name="tax_rate" value="">
<input type="hidden" name="shipping" value="0"><input type="hidden" name="shipping2" value="">
<input type="hidden" name="handling_cart" value="">
<input type="hidden" name="item_name" value="7x12 White RimGard ">
<input type="hidden" name="item_number" value="rimgard0712">
<input type="hidden" name="amount" value="138">


<input type="hidden" name="discount_amount" value="0">





<input type="hidden" name="add" value="1">

<input type="hidden" name="weight_unit" value="lbs">
<input type="hidden" name="cmd" value="_cart">
<input type="hidden" name="bn" value="RageSoftware_SP">


<input onclick=getContinueShoppingURL(this.form) type="image" src="https://www.paypalobjects.com/webstatic/en_US/i/btn/png/btn_addtocart_120x26.png" border="0" name="submit" alt="Add To Cart">

<img alt="" border="0" src="https://www.paypalobjects.com/en_US/i/scr/pixel.gif" width="1" height="1">



</form>
</div></div></div><div style="position:relative"><div class="shape_56" style="left:756px;top:889px;width:142px;height:34px;z-index:56;position: absolute;"><div class="paraWrap" style="padding: 0px 2.16px 0px 2.16px; "><p class="para27"><span style="line-height: 11.8px;" class="Style128">Cost: $64 Each</span></p></div></div></div><div style="position:relative"><div class="shape_57" style="left:94px;top:889px;width:654px;height:92px;z-index:57;position: absolute;"><div class="paraWrap" style="padding: 0px 2.16px 0px 2.16px; "><p class="para126"><span style="line-height: 19.8px;" class="Style127">Add Swimmer's Lane Marker</span><span style="line-height: 19.8px;" class="Style128">:</span><span style="line-height: 15.84px;" class="Style129">&nbsp;&nbsp; </span><span style="line-height: 21.78px;" class="Style54">This will add a 10" wide black swimmer's lane marker to the floor of your EZ Pool. Olympic standards are eight feet wide swimming lanes, however personal pools can go as tight as five feet wide. Choose how many lanes you would like. If you are not sure, feel free to </span><span style="line-height: 21.78px;" class="linkStyle_55"><a href="contact-us.html?iframe=true&width=300&height=300" data-linkuuid="D421732F30D942A9BAC71356999047EC" rel="ewpopup"  class="linkStyle_55">Reach Out to Us</a></span><span style="line-height: 21.78px;" class="Style54">.</span></p></div></div></div><div style="position:relative"><div style="left:748px;top:911px;height:26px;width:164px;position: absolute;z-index: 58;" id="custom-background-58"><!--164-->
<div style="width:100%;text-align:Center">



<form class="ew_pp ppOrderForm_58" id="ppOrderForm_58" action="https://www.paypal.com/cgi-bin/webscr" method="post" target="paypal">

<input type="hidden" name="business" value="<EMAIL>">

<input type="hidden" name="currency_code" value="USD">
<input type="hidden" name="button_subtype" value="product">
<input type="hidden" name="no_note" value="0">
<input type="hidden" name="no_shipping" value="2">



<input type="hidden" name="shopping_url" value="#">




Quantity:<br /><input type="text" name="quantity" size="6" value=""><br /><br />






<input type="hidden" name="tax_rate" value="">
<input type="hidden" name="shipping" value="0"><input type="hidden" name="shipping2" value="">
<input type="hidden" name="handling_cart" value="">
<input type="hidden" name="item_name" value="Add 10 Inch Wide Black Swimmer's Lane">
<input type="hidden" name="item_number" value="lane">
<input type="hidden" name="amount" value="64">


<input type="hidden" name="discount_amount" value="0">





<input type="hidden" name="add" value="1">

<input type="hidden" name="weight_unit" value="lbs">
<input type="hidden" name="cmd" value="_cart">
<input type="hidden" name="bn" value="RageSoftware_SP">


<input onclick=getContinueShoppingURL(this.form) type="image" src="https://www.paypalobjects.com/webstatic/en_US/i/btn/png/btn_addtocart_120x26.png" border="0" name="submit" alt="Add To Cart">

<img alt="" border="0" src="https://www.paypalobjects.com/en_US/i/scr/pixel.gif" width="1" height="1">



</form>
</div></div></div><div style="position:relative"><div class="shape_59" style="left:784px;top:1030px;width:95px;height:22px;z-index:59;position: absolute;"><div class="paraWrap" style="padding: 0px 2.16px 0px 2.16px; "><p class="para27"><span style="line-height: 11.8px;" class="Style128">Cost: $500</span></p></div></div></div><div style="position:relative"><div class="shape_60" style="left:93px;top:1016px;width:654px;height:92px;z-index:60;position: absolute;"><div class="paraWrap" style="padding: 0px 2.16px 0px 2.16px; "><p class="para126"><span style="line-height: 19.8px;" class="Style127">Request Modification</span><span style="line-height: 19.8px;" class="Style128">:</span><span style="line-height: 15.84px;" class="Style129">&nbsp;&nbsp; </span><span style="line-height: 21.78px;" class="Style54">Because your EZ Pool is </span><span style="line-height: 21.78px;" class="linkStyle_55"><a href="custom-pools.html?iframe=true&width=600&height=300" data-linkuuid="4241C4683D094A73A58B25E3B0EC7520" rel="ewpopup"  class="linkStyle_55">Made-to-Order</a></span><span style="line-height: 21.78px;" class="Style54"> we are able to modify the width, length and/or depth of your EZ Pool. We only modify down, not up. If you choose this option you will receive a follow up email requesting more details on your request.</span></p></div></div></div><div style="position:relative"><div style="left:772px;top:1052px;height:26px;width:107px;position: absolute;z-index: 61;" id="custom-background-61"><!--107-->
<div style="width:100%;text-align:Center">



<form class="ew_pp ppOrderForm_61" id="ppOrderForm_61" action="https://www.paypal.com/cgi-bin/webscr" method="post" target="paypal">

<input type="hidden" name="business" value="<EMAIL>">

<input type="hidden" name="currency_code" value="USD">
<input type="hidden" name="button_subtype" value="product">
<input type="hidden" name="no_note" value="0">
<input type="hidden" name="no_shipping" value="2">



<input type="hidden" name="shopping_url" value="#">











<input type="hidden" name="tax_rate" value="">
<input type="hidden" name="shipping" value="0"><input type="hidden" name="shipping2" value="">
<input type="hidden" name="handling_cart" value="">
<input type="hidden" name="item_name" value="Modification Request">
<input type="hidden" name="item_number" value="modify">
<input type="hidden" name="amount" value="500">


<input type="hidden" name="discount_amount" value="0">





<input type="hidden" name="add" value="1">

<input type="hidden" name="weight_unit" value="lbs">
<input type="hidden" name="cmd" value="_cart">
<input type="hidden" name="bn" value="RageSoftware_SP">


<input onclick=getContinueShoppingURL(this.form) type="image" src="https://www.paypalobjects.com/webstatic/en_US/i/btn/png/btn_addtocart_120x26.png" border="0" name="submit" alt="Add To Cart">

<img alt="" border="0" src="https://www.paypalobjects.com/en_US/i/scr/pixel.gif" width="1" height="1">



</form>
</div></div></div><div style="position:relative"><div class="shape_62" style="left:112.5px;top:865.5px;width:760px;width:760px;margin-top:8.5px;z-index:62;position: absolute;"></div></div><div style="position:relative"><div class="shape_63" style="left:112.5px;top:993.5px;width:760px;width:760px;margin-top:8.5px;z-index:63;position: absolute;"></div></div><div style="position:relative"><div class="shape_64" style="left:519px;top:1715px;width:107px;height:22px;z-index:64;position: absolute;"><div class="paraWrap" style="padding: 0px 2.16px 0px 2.16px; "><p class="para27"><span style="line-height: 11.8px;" class="Style128">Add: $1000</span></p></div></div></div><div style="position:relative"><div class="shape_65" style="left:762px;top:1712px;width:107px;height:22px;z-index:65;position: absolute;"><div class="paraWrap" style="padding: 0px 2.16px 0px 2.16px; "><p class="para27"><span style="line-height: 11.8px;" class="Style128">Add: $100</span></p></div></div></div><div style="position:relative"><div class="shape_66" style="left:64.5px;top:1579.5px;width:185px;height:169px;width:183px;height:167px;z-index:66;position: absolute;"></div></div><div style="position:relative"><div class="shape_67" style="left:65px;top:1605px;width:184px;height:22px;z-index:67;position: absolute;"><div class="paraWrap" style="padding: 0px 2.16px 0px 2.16px; "><p class="para27"><span style="line-height: 11.8px;" class="Style128">1HP 50 sq ft P/F</span><span style="line-height: 11.8px;" class="Style143"> </span></p></div></div></div><div style="position:relative"><div class="shape_68" style="left:79px;top:1634px;width:162px;height:54px;z-index:68;position: absolute;"><div class="paraWrap" style="padding: 0px 2.16px 0px 2.16px; "><p class="para126"><span style="line-height: 12px;" class="Style148">√</span><span style="line-height: 12px;" class="Style132"> Filters 50 Gallons per Minute</span></p><p class="para126"><span style="line-height: 12px;" class="Style148">√</span><span style="line-height: 12px;" class="Style132"> Reusable Polyester Element</span></p><p class="para126"><span style="line-height: 12px;" class="Style148">√</span><span style="line-height: 12px;" class="Style132"> Fits regular 110v Outlet</span></p></div></div></div><div style="position:relative"><div class="shape_69" style="left:105px;top:1712px;width:107px;height:22px;z-index:69;position: absolute;"><div class="paraWrap" style="padding: 0px 2.16px 0px 2.16px; "><p class="para27"><span style="line-height: 11.8px;" class="Style128">Add: $500</span></p></div></div></div><div style="position:relative"><div class="shape_70" style="left:73px;top:1509px;width:840px;height:84px;z-index:70;position: absolute;"><img src="images/7x12/AccessoriesBorder-1.png" height="84" width="840" data-src2x="images/7x12/<EMAIL>" srcset="images/7x12/AccessoriesBorder-1.png 1x, images/7x12/<EMAIL> 2x" /></div></div><div style="position:relative"><a href="1050.html?iframe=true&width=300&height=300" data-linkuuid="43E50EE04FD34FB2B05821F74826E187" rel="ewpopup" class="outerlink"><div class="shape_71" style="left:73px;top:1689px;width:163px;height:13px;z-index:71;position: absolute;"><div class="paraWrap" style="padding: 0px 2.16px 0px 2.16px; "><p class="para52"><span style="line-height: 13px;" class="linkStyle_145"><a href="1050.html?iframe=true&width=600&height=600" data-linkuuid="43E50EE04FD34FB2B05821F74826E187" rel="ewpopup"  class="linkStyle_145">Click Here for More Info:</a></span></p></div></div></a></div><div style="position:relative"><div style="left:102px;top:1735px;height:26px;width:107px;position: absolute;z-index: 72;" id="custom-background-72"><!--107-->
<div style="width:100%;text-align:Center">



<form class="ew_pp ppOrderForm_72" id="ppOrderForm_72" action="https://www.paypal.com/cgi-bin/webscr" method="post" target="paypal">

<input type="hidden" name="business" value="<EMAIL>">

<input type="hidden" name="currency_code" value="USD">
<input type="hidden" name="button_subtype" value="product">
<input type="hidden" name="no_note" value="0">
<input type="hidden" name="no_shipping" value="2">



<input type="hidden" name="shopping_url" value="#">











<input type="hidden" name="tax_rate" value="">
<input type="hidden" name="shipping" value=""><input type="hidden" name="shipping2" value="">
<input type="hidden" name="handling_cart" value="">
<input type="hidden" name="item_name" value="1HP Pump 50 Sq Ft Filter">
<input type="hidden" name="item_number" value="1050">
<input type="hidden" name="amount" value="500">


<input type="hidden" name="discount_amount" value="0">





<input type="hidden" name="add" value="1">
<input type="hidden" name="weight" value="15">
<input type="hidden" name="weight_unit" value="lbs">
<input type="hidden" name="cmd" value="_cart">
<input type="hidden" name="bn" value="RageSoftware_SP">


<input onclick=getContinueShoppingURL(this.form) type="image" src="https://www.paypalobjects.com/webstatic/en_US/i/btn/png/btn_addtocart_120x26.png" border="0" name="submit" alt="Add To Cart">

<img alt="" border="0" src="https://www.paypalobjects.com/en_US/i/scr/pixel.gif" width="1" height="1">



</form>
</div></div></div><div style="position:relative"><div class="shape_73" style="left:265.5px;top:1610.5px;width:192px;height:137px;width:190px;height:135px;z-index:73;position: absolute;"></div></div><div style="position:relative"><div style="left:299px;top:1734px;height:50px;width:107px;position: absolute;z-index: 74;" id="custom-background-74"><!--107-->
<div style="width:100%;text-align:Center">



<form class="ew_pp ppOrderForm_74" id="ppOrderForm_74" action="https://www.paypal.com/cgi-bin/webscr" method="post" target="paypal">

<input type="hidden" name="business" value="<EMAIL>">

<input type="hidden" name="currency_code" value="USD">
<input type="hidden" name="button_subtype" value="product">
<input type="hidden" name="no_note" value="0">
<input type="hidden" name="no_shipping" value="2">



<input type="hidden" name="shopping_url" value="#">











<input type="hidden" name="tax_rate" value="">
<input type="hidden" name="shipping" value=""><input type="hidden" name="shipping2" value="">
<input type="hidden" name="handling_cart" value="">
<input type="hidden" name="item_name" value="Extra 50 Sq Ft Cartridge">
<input type="hidden" name="item_number" value="50Element">
<input type="hidden" name="amount" value="80">


<input type="hidden" name="discount_amount" value="0">





<input type="hidden" name="add" value="1">
<input type="hidden" name="weight" value="5">
<input type="hidden" name="weight_unit" value="lbs">
<input type="hidden" name="cmd" value="_cart">
<input type="hidden" name="bn" value="RageSoftware_SP">


<input onclick=getContinueShoppingURL(this.form) type="image" src="https://www.paypalobjects.com/webstatic/en_US/i/btn/png/btn_addtocart_120x26.png" border="0" name="submit" alt="Add To Cart">

<img alt="" border="0" src="https://www.paypalobjects.com/en_US/i/scr/pixel.gif" width="1" height="1">



</form>
</div></div></div><div style="position:relative"><div class="shape_75" style="left:266px;top:1628px;width:191px;height:22px;z-index:75;position: absolute;"><div class="paraWrap" style="padding: 0px 2.16px 0px 2.16px; "><p class="para27"><span style="line-height: 11.8px;" class="Style141">Extra Cartridge </span><span style="line-height: 7.67px;" class="Style142">(for 50sf)</span></p></div></div></div><div style="position:relative"><div class="shape_76" style="left:275px;top:1655px;width:196px;height:35px;z-index:76;position: absolute;"><div class="paraWrap" style="padding: 0px 2.16px 0px 2.16px; "><p class="para126"><span style="line-height: 12px;" class="Style148">√</span><span style="line-height: 12px;" class="Style132"> Cleanable, Durable & Reusable</span></p><p class="para126"><span style="line-height: 12px;" class="Style148">√</span><span style="line-height: 12px;" class="Style132"> Handy to have an Extra One</span></p></div></div></div><div style="position:relative"><div class="shape_77" style="left:300px;top:1712px;width:107px;height:22px;z-index:77;position: absolute;"><div class="paraWrap" style="padding: 0px 2.16px 0px 2.16px; "><p class="para27"><span style="line-height: 11.8px;" class="Style128">Add: $80</span></p></div></div></div><div style="position:relative"><div style="left:546px;top:573.125px;height:57px;width:151px;position: absolute;z-index: 78;" id="custom-background-78"><!--151-->
<div style="width:100%;text-align:Left">



<form class="ew_pp ppOrderForm_78" id="ppOrderForm_78" action="https://www.paypal.com/cgi-bin/webscr" method="post" target="paypal">

<input type="hidden" name="business" value="<EMAIL>">

<input type="hidden" name="currency_code" value="USD">
<input type="hidden" name="button_subtype" value="product">
<input type="hidden" name="no_note" value="0">
<input type="hidden" name="no_shipping" value="0">



<input type="hidden" name="shopping_url" value="#">











<input type="hidden" name="tax_rate" value="">
<input type="hidden" name="shipping" value="450">
<input type="hidden" name="handling_cart" value="">
<input type="hidden" name="item_name" value="7' x 12' EZ Pool">
<input type="hidden" name="item_number" value="71252">
<input type="hidden" name="amount" value="1650">


<input type="hidden" name="discount_amount" value="0">





<input type="hidden" name="add" value="1">
<input type="hidden" name="weight" value="500">
<input type="hidden" name="weight_unit" value="lbs">
<input type="hidden" name="cmd" value="_cart">
<input type="hidden" name="bn" value="RageSoftware_SP">



<input onclick=getContinueShoppingURL(this.form) type="image" src="ewExternalFiles/Cart1.png" border="0" name="submit" width="375" height="150" alt="Add To Cart"/>
<img alt="" border="0" src="https://www.paypalobjects.com/en_US/i/scr/pixel.gif" width="1" height="1">



</form>
</div></div></div><div style="position:relative"><div class="shape_79" style="left:4px;top:303px;width:45px;height:1822px;z-index:79;position: absolute;"><img src="images/7x12/left.jpg" height="1822" width="45" /></div></div><div style="position:relative"><div class="ewnavmenu" id="navmenu_menu0" style="left:29px;top:2173px;height:44px;width:946px;z-index:10080;position: absolute;"><ul id="navigation_menu0"><li><a class="main item0" href="big-pools.html" data-linkuuid="410BBB30E1F845799392B660E131B41E">Big Pools</a></li><li><a class="main item1" href="custom-pools.html" data-linkuuid="4241C4683D094A73A58B25E3B0EC7520">Custom Pools</a></li><li><a class="main item2" href="dog-pools.html" data-linkuuid="0CB7E371B49049BA934F975B600D014C">Dog Pools</a></li><li><a class="main item3" href="event-pools.html" data-linkuuid="CD0DE9F8DF8A43C89892C4B8A71F6CEF">Event Pools</a></li><li><a class="main item4" href="ez-lap-pools.html" data-linkuuid="04F91FAF367F438BB79D438D055F62DA">Lap Pools</a></li><li><a class="main item5" href="our-store.html" data-linkuuid="40A7646C2F5B4E3F8184D08BEEA81B75">Our Store</a></li><li><a class="main item6" href="terms-of-sale.html" data-linkuuid="353687093D284D67BAFFBDF1E3486421">Terms of Sale</a></li><li  style='margin-right:0px;' class="last"><a class="main item7" href="warranty.html" data-linkuuid="43D1BF25435448F381F5D8076B17A7B0">Warranty</a></li></ul></div></div><div style="position:relative"><div class="shape_81" style="left:4px;top:2125px;width:992px;height:48px;z-index:81;position: absolute;"><img src="images/7x12/bottom21.jpg" height="48" width="992" /></div></div><div style="position:relative"><div class="shape_82" style="left:956px;top:304px;width:40px;height:1821px;z-index:82;position: absolute;"><img src="images/7x12/right.jpg" height="1821" width="40" /></div></div><div style="position:relative"><div class="shape_83" style="left:4px;top:211px;width:992px;height:93px;z-index:83;position: absolute;"><img src="images/7x12/7x12Price-1.png" height="93" width="992" /></div></div><div style="position:relative"><div class="shape_85" style="left:76px;top:324px;width:844px;height:250px;width:840px;height:246px;z-index:85;position: absolute;"></div></div><div style="position:relative"><div class="shape_86" style="left:102px;top:326px;width:336px;height:235px;z-index:86;position: absolute;"><img src="images/7x12/7.jpg" height="235" width="336" data-src2x="images/7x12/<EMAIL>" srcset="images/7x12/7.jpg 1x, images/7x12/<EMAIL> 2x" /></div></div><div style="position:relative"><a href="ewExternalFiles/7x12-1.pdf" class="outerlink"><div class="shape_87" style="left:240.5px;top:1381.5px;width:499px;height:31px;z-index:87;position: absolute;"></div></a></div><div style="position:relative"><a href="ewExternalFiles/7x12-1.pdf" class="outerlink"><div class="shape_88" style="left:269px;top:1383px;width:455px;height:38px;z-index:88;position: absolute;"><div class="paraWrap" style="padding: 0px 2.16px 0px 2.16px; "><p class="para27"><span style="line-height: 28px;" class="Style156">Click Here for a PDF Brochure </span><span style="line-height: 16px;" class="Style157">(3.5mbs)</span></p></div></div></a></div><div style="position:relative"><a href="7-wide-lap-pools.html" data-linkuuid="41C14181C1E5419BB204148205FA4F3D" class="outerlink"><div class="shape_89" style="left:88px;top:98px;width:204px;height:61px;z-index:89;position: absolute;"><img src="images/7x12/27a.gif" height="61" width="204" onmouseover="this.src='images/7x12/<EMAIL>';this.srcset=''" onmouseout="this.src='images/7x12/27a.gif'" /></div></a></div><div style="position:relative"><a href="12-wide-family-pools.html" data-linkuuid="B8618C443B2A4B3A939419DDC8B6215D" class="outerlink"><div class="shape_90" style="left:292px;top:98px;width:212px;height:61px;z-index:90;position: absolute;"><img src="images/7x12/28a.gif" height="61" width="212" onmouseover="this.src='images/7x12/<EMAIL>';this.srcset=''" onmouseout="this.src='images/7x12/28a.gif'" /></div></a></div><div style="position:relative"><a href="17-wide-super-pools.html" data-linkuuid="B05EF65A5FEE4F73A5C82BA012B351D0" class="outerlink"><div class="shape_91" style="left:504px;top:98px;width:206px;height:61px;z-index:91;position: absolute;"><img src="images/7x12/29a.gif" height="61" width="206" onmouseover="this.src='images/7x12/<EMAIL>';this.srcset=''" onmouseout="this.src='images/7x12/29a.gif'" /></div></a></div><div style="position:relative"><a href="22-wide-giant-pools.html" data-linkuuid="863A865B89684874A751B27AAC1122FA" class="outerlink"><div class="shape_92" style="left:710px;top:98px;width:204px;height:61px;z-index:92;position: absolute;"><img src="images/7x12/30a.gif" height="61" width="204" onmouseover="this.src='images/7x12/<EMAIL>';this.srcset=''" onmouseout="this.src='images/7x12/30a.gif'" /></div></a></div><div style="position:relative"><div style="left:73px;top:1893px;height:27px;width:107px;position: absolute;z-index: 93;" id="custom-background-93"><!--107-->
<div style="width:100%;text-align:Center">



<form class="ew_pp ppOrderForm_93" id="ppOrderForm_93" action="https://www.paypal.com/cgi-bin/webscr" method="post" target="paypal">

<input type="hidden" name="business" value="<EMAIL>">

<input type="hidden" name="currency_code" value="USD">
<input type="hidden" name="button_subtype" value="product">
<input type="hidden" name="no_note" value="0">
<input type="hidden" name="no_shipping" value="2">



<input type="hidden" name="shopping_url" value="#">











<input type="hidden" name="tax_rate" value="">
<input type="hidden" name="shipping" value="35"><input type="hidden" name="shipping2" value="">
<input type="hidden" name="handling_cart" value="">
<input type="hidden" name="item_name" value="A-Frame Ldder">
<input type="hidden" name="item_number" value="ladder">
<input type="hidden" name="amount" value="400">


<input type="hidden" name="discount_amount" value="0">





<input type="hidden" name="add" value="1">
<input type="hidden" name="weight" value="25">
<input type="hidden" name="weight_unit" value="lbs">
<input type="hidden" name="cmd" value="_cart">
<input type="hidden" name="bn" value="RageSoftware_SP">


<input onclick=getContinueShoppingURL(this.form) type="image" src="https://www.paypalobjects.com/webstatic/en_US/i/btn/png/btn_addtocart_120x26.png" border="0" name="submit" alt="Add To Cart">

<img alt="" border="0" src="https://www.paypalobjects.com/en_US/i/scr/pixel.gif" width="1" height="1">



</form>
</div></div></div><div style="position:relative"><div class="shape_94" style="left:756px;top:1132px;width:136px;height:34px;z-index:94;position: absolute;"><div class="paraWrap" style="padding: 0px 2.16px 0px 2.16px; "><p class="para27"><span style="line-height: 11.8px;" class="Style128">Cost: $200-$400</span></p></div></div></div><div style="position:relative"><div style="left:772px;top:1155px;height:26px;width:107px;position: absolute;z-index: 95;" id="custom-background-95"><!--107-->
<div style="width:100%;text-align:Center">



<form class="ew_pp ppOrderForm_95" id="ppOrderForm_95" action="https://www.paypal.com/cgi-bin/webscr" method="post" target="paypal">

<input type="hidden" name="business" value="<EMAIL>">

<input type="hidden" name="currency_code" value="USD">
<input type="hidden" name="button_subtype" value="product">
<input type="hidden" name="no_note" value="0">
<input type="hidden" name="no_shipping" value="2">



<input type="hidden" name="shopping_url" value="#">
<input type="hidden" name="on0" value="Color Choices">Color Choices<br /><select name="os0"> <option value="Black">Black - 200.00</option> <option value="Dark-Blue">Dark Blue - 200.00</option> <option value="Gray">Gray - 200.00</option> <option value="White">White - 200.00</option> <option value="Tan/Beige">Tan/Beige - 300.00</option> <option value="Brown">Brown - 300.00</option> <option value="Dark-Green">Dark Green - 300.00</option> <option value="Pink">Pink - 400.00</option> <option value="Burgundy">Burgundy - 400.00</option> <option value="Yellow">Yellow - 400.00</option> <option value="Red">Red - 400.00</option> <option value="Orange">Orange - 400.00</option> <option value="Light-Green">Light Green - 400.00</option></select><br /><input type="hidden" name="option_index" value="0"><input type="hidden" name="option_select0" value="Black"><input type="hidden" name="option_amount0" value="200.00"><input type="hidden" name="option_select1" value="Dark-Blue"><input type="hidden" name="option_amount1" value="200.00"><input type="hidden" name="option_select2" value="Gray"><input type="hidden" name="option_amount2" value="200.00"><input type="hidden" name="option_select3" value="White"><input type="hidden" name="option_amount3" value="200.00"><input type="hidden" name="option_select4" value="Tan/Beige"><input type="hidden" name="option_amount4" value="300.00"><input type="hidden" name="option_select5" value="Brown"><input type="hidden" name="option_amount5" value="300.00"><input type="hidden" name="option_select6" value="Dark-Green"><input type="hidden" name="option_amount6" value="300.00"><input type="hidden" name="option_select7" value="Pink"><input type="hidden" name="option_amount7" value="400.00"><input type="hidden" name="option_select8" value="Burgundy"><input type="hidden" name="option_amount8" value="400.00"><input type="hidden" name="option_select9" value="Yellow"><input type="hidden" name="option_amount9" value="400.00"><input type="hidden" name="option_select10" value="Red"><input type="hidden" name="option_amount10" value="400.00"><input type="hidden" name="option_select11" value="Orange"><input type="hidden" name="option_amount11" value="400.00"><input type="hidden" name="option_select12" value="Light-Green"><input type="hidden" name="option_amount12" value="400.00"><br />










<input type="hidden" name="tax_rate" value="">
<input type="hidden" name="shipping" value="0">
<input type="hidden" name="handling_cart" value="">
<input type="hidden" name="item_name" value="Color Change">
<input type="hidden" name="item_number" value="modify">
<input type="hidden" name="amount" value="500">


<input type="hidden" name="discount_amount" value="0">





<input type="hidden" name="add" value="1">

<input type="hidden" name="weight_unit" value="lbs">
<input type="hidden" name="cmd" value="_cart">
<input type="hidden" name="bn" value="RageSoftware_SP">


<input onclick=getContinueShoppingURL(this.form) type="image" src="https://www.paypalobjects.com/webstatic/en_US/i/btn/png/btn_addtocart_120x26.png" border="0" name="submit" alt="Add To Cart">

<img alt="" border="0" src="https://www.paypalobjects.com/en_US/i/scr/pixel.gif" width="1" height="1">



</form>
</div></div></div><div style="position:relative"><div class="shape_96" style="left:112.5px;top:1108.5px;width:760px;width:760px;margin-top:8.5px;z-index:96;position: absolute;"></div></div><div style="position:relative"><div class="shape_97" style="left:93px;top:1132px;width:654px;height:114px;z-index:97;position: absolute;"><div class="paraWrap" style="padding: 0px 2.16px 0px 2.16px; "><p class="para126"><span style="line-height: 19.8px;" class="Style127">Request Color Change</span><span style="line-height: 19.8px;" class="Style128">:</span><span style="line-height: 15.84px;" class="Style129">&nbsp;&nbsp; </span><span style="line-height: 21.78px;" class="Style54">We offer a variety of color options; </span><span style="line-height: 21.78px;" class="linkStyle_55"><a href="color-chart.html?iframe=true&width=800&height=600" data-linkuuid="6011E047AA204536B8030BB3C5AA1C7A" rel="ewpopup"  class="linkStyle_55">Click Here to View our Available Colors</a></span><span style="line-height: 21.78px;" class="Style54">. You can also choose direct colors for the wall and the floor of the both for the same price. But please understand the color of the wall outside is the same as the inside, with the exception of the floor, which can be a different color. </span><span style="line-height: 21.78px;" class="Style158">We do recommend the RimGard for dark colored pool walls such as: black, dark blue, etc...</span></p></div></div></div><div style="position:relative"><div class="shape_98" style="left:772px;top:1283px;width:107px;height:22px;z-index:98;position: absolute;"><div class="paraWrap" style="padding: 0px 2.16px 0px 2.16px; "><p class="para27"><span style="line-height: 11.8px;" class="Style128">Cost: $1,000</span></p></div></div></div><div style="position:relative"><div class="shape_99" style="left:333px;top:1279px;width:429px;height:77px;z-index:99;position: absolute;"><div class="paraWrap" style="padding: 0px 2.16px 0px 2.16px; "><p class="para126"><span style="line-height: 21.78px;" class="Style54">Add this to include: 1HP Pump/Filter, A-Frame Ladder, Care Kit, Extra Cartridge, Solar Cover and </span><span style="line-height: 24.75px;" class="Style159">SAVE $170</span><span style="line-height: 21.78px;" class="Style54"> compared to the individual item costs.</span></p></div></div></div><div style="position:relative"><div style="left:772px;top:1305px;height:26px;width:107px;position: absolute;z-index: 100;" id="custom-background-100"><!--107-->
<div style="width:100%;text-align:Center">



<form class="ew_pp ppOrderForm_100" id="ppOrderForm_100" action="https://www.paypal.com/cgi-bin/webscr" method="post" target="paypal">

<input type="hidden" name="business" value="<EMAIL>">

<input type="hidden" name="currency_code" value="USD">
<input type="hidden" name="button_subtype" value="product">
<input type="hidden" name="no_note" value="0">
<input type="hidden" name="no_shipping" value="2">



<input type="hidden" name="shopping_url" value="#">











<input type="hidden" name="tax_rate" value="">
<input type="hidden" name="shipping" value="100"><input type="hidden" name="shipping2" value="">
<input type="hidden" name="handling_cart" value="">
<input type="hidden" name="item_name" value="Add Staycation Package">
<input type="hidden" name="item_number" value="staycation">
<input type="hidden" name="amount" value="1000">


<input type="hidden" name="discount_amount" value="0">





<input type="hidden" name="add" value="1">

<input type="hidden" name="weight_unit" value="lbs">
<input type="hidden" name="cmd" value="_cart">
<input type="hidden" name="bn" value="RageSoftware_SP">


<input onclick=getContinueShoppingURL(this.form) type="image" src="https://www.paypalobjects.com/webstatic/en_US/i/btn/png/btn_addtocart_120x26.png" border="0" name="submit" alt="Add To Cart">

<img alt="" border="0" src="https://www.paypalobjects.com/en_US/i/scr/pixel.gif" width="1" height="1">



</form>
</div></div></div><div style="position:relative"><div class="shape_101" style="left:112.5px;top:1252.5px;width:760px;width:760px;margin-top:8.5px;z-index:101;position: absolute;"></div></div><div style="position:relative"><div class="shape_102" style="left:93px;top:1269px;width:221px;height:118px;z-index:102;position: absolute;"><img src="images/7x12/PackageDealsBug-1.png" height="118" width="221" /></div></div><div style="position:relative"><div class="shape_103" style="left:79px;top:1994px;width:183px;height:26px;z-index:103;position: absolute;"><div class="paraWrap" style="padding: 0px 2.16px 0px 2.16px; "><p class="para27"><span style="line-height: 16px;" class="linkStyle_160"><a href="inpool-skimmer.html" data-linkuuid="FD9402C9013D4271A3320D6289992674" target="_blank"  class="linkStyle_160">Learn More&gt;&gt;</a></span></p></div></div></div><div style="position:relative"><div class="shape_104" style="left:438px;top:326px;width:462px;height:242px;z-index:104;position: absolute;"><img src="images/7x12/12-1.jpg" height="242" width="462" /></div></div><div style="position:relative"><div style="left:242px;top:182.25px;height:41px;width:560px;position: absolute;z-index: 105;" id="custom-background-105">
<!-- In your code, dynamically update
data-pp-amount with the price or cart amount.
For example, data-pp-amount="89.99"
for $89.99 product
-->
<div
    data-pp-message
    data-pp-style-layout="text"
    data-pp-style-logo-type="inline"
    data-pp-style-text-color="black"
    data-pp-amount="ENTER_VALUE_HERE">
</div></div></div><div style="position:relative"><div class="shape_106" style="left:41px;top:2131px;width:929px;height:28px;z-index:106;position: absolute;"><div class="paraWrap" style="padding: 0px 2.16px 0px 2.16px; "><p class="para27"><span style="line-height: 18px;" class="Style46">® 2023 Registered, All Rights Reserved</span></p></div></div></div><div style="position:relative"><div style="left:105px;top:2073px;height:27px;width:107px;position: absolute;z-index: 107;" id="custom-background-107"><!--107-->
<div style="width:100%;text-align:Center">



<form class="ew_pp ppOrderForm_107" id="ppOrderForm_107" action="https://www.paypal.com/cgi-bin/webscr" method="post" target="paypal">

<input type="hidden" name="business" value="<EMAIL>">

<input type="hidden" name="currency_code" value="USD">
<input type="hidden" name="button_subtype" value="product">
<input type="hidden" name="no_note" value="0">
<input type="hidden" name="no_shipping" value="2">



<input type="hidden" name="shopping_url" value="#">











<input type="hidden" name="tax_rate" value="">
<input type="hidden" name="shipping" value="0"><input type="hidden" name="shipping2" value="">
<input type="hidden" name="handling_cart" value="">
<input type="hidden" name="item_name" value="InPool Skimmer">
<input type="hidden" name="item_number" value="inpoolskim">
<input type="hidden" name="amount" value="175">


<input type="hidden" name="discount_amount" value="0">





<input type="hidden" name="add" value="1">
<input type="hidden" name="weight" value="1">
<input type="hidden" name="weight_unit" value="lbs">
<input type="hidden" name="cmd" value="_cart">
<input type="hidden" name="bn" value="RageSoftware_SP">


<input onclick=getContinueShoppingURL(this.form) type="image" src="https://www.paypalobjects.com/webstatic/en_US/i/btn/png/btn_addtocart_120x26.png" border="0" name="submit" alt="Add To Cart">

<img alt="" border="0" src="https://www.paypalobjects.com/en_US/i/scr/pixel.gif" width="1" height="1">



</form>
</div></div></div><div style="position:relative"><div class="shape_108" style="left:65px;top:2020px;width:197px;height:52px;z-index:108;position: absolute;"><div class="paraWrap" style="padding: 0px 2.16px 0px 2.16px; "><p class="para131"><span style="line-height: 12px;" class="Style148">√</span><span style="line-height: 12px;" class="Style132"> Fits Snug in the Pool Corner</span></p><p class="para131"><span style="line-height: 12px;" class="Style148">√</span><span style="line-height: 12px;" class="Style132"> Typically does not Interefer</span></p><p class="para131"><span style="line-height: 12px;" class="Style132">with swimming.</span></p></div></div></div></div><footer data-top='2216' data-height='97'><div style="position:relative"><div class="shape_109" style="left:302px;top:2217px;width:395px;height:97px;z-index:109;position: absolute;"><img src="images/7x12/Logo-109.png" height="97" width="395" data-src2x="images/7x12/<EMAIL>" srcset="images/7x12/Logo-109.png 1x, images/7x12/<EMAIL> 2x" /></div></div></footer></div><script>function preloader() {if (document.images) {var i0=new Image();i0.src ='images/7x12/<EMAIL>';var i1=new Image();i1.src ='images/7x12/<EMAIL>';var i2=new Image();i2.src ='images/7x12/<EMAIL>';var i3=new Image();i3.src ='images/7x12/<EMAIL>';}}function addLoadEvent(func) {window.onload = func;}addLoadEvent(preloader);</script></body></html>