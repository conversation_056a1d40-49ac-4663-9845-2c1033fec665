#!/bin/bash
set -euo pipefail

# Convert Windows line endings to Unix (in case files were edited on Windows)
find /var/www/html -name "*.php" -type f -exec dos2unix {} \; 2>/dev/null || true

# Wait for database to be ready
echo "Waiting for database to be ready..."
while ! mysqladmin ping -h"$WORDPRESS_DB_HOST" --silent; do
    sleep 1
done
echo "Database is ready!"

# Start Apache in background
apache2-foreground &

# Wait for WordPress to be accessible
echo "Waiting for WordPress to be ready..."
while ! curl -s http://localhost > /dev/null; do
    sleep 1
done
echo "WordPress is ready!"

# Check if WordPress is installed
if ! wp core is-installed --allow-root --path=/var/www/html 2>/dev/null; then
    echo "Installing WordPress..."
    
    # Install WordPress
    wp core install \
        --url="http://localhost:8080" \
        --title="EZ Pools - The Better Portable Pool" \
        --admin_user="admin" \
        --admin_password="admin123" \
        --admin_email="<EMAIL>" \
        --allow-root \
        --path=/var/www/html
    
    echo "WordPress installed successfully!"
    
    # Activate EZ Pools theme
    if wp theme is-installed ezpools --allow-root --path=/var/www/html; then
        wp theme activate ezpools --allow-root --path=/var/www/html
        echo "EZ Pools theme activated!"
    else
        echo "EZ Pools theme not found, using default theme"
    fi
    
    # Run setup script if it exists
    if [ -f "/var/www/html/setup.php" ]; then
        echo "Running EZ Pools setup..."
        wp eval-file setup.php --allow-root --path=/var/www/html
        echo "EZ Pools setup completed!"
    fi
    
    # Set permalinks to pretty URLs
    wp rewrite structure '/%postname%/' --allow-root --path=/var/www/html
    wp rewrite flush --allow-root --path=/var/www/html
    
    echo "Setup complete! You can now access your site at http://localhost:8080"
    echo "WordPress Admin: http://localhost:8080/wp-admin"
    echo "Username: admin"
    echo "Password: admin123"
else
    echo "WordPress is already installed"
fi

# Keep the container running
wait
