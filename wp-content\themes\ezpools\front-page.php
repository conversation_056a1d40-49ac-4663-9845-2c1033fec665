<?php get_header(); ?>

<!-- Modern Hero Section -->
<section id="home" class="hero">
    <div class="hero-background"></div>
    <div class="container">
        <h1><?php echo get_theme_mod('hero_title', 'The Better Portable Pool'); ?></h1>
        <p><?php echo get_theme_mod('hero_subtitle', 'Custom portable pools made-to-order. From carton to completion in less than one hour. All the features of traditional pools at 1/10th the cost.'); ?></p>
        <div class="hero-buttons">
            <a href="#contact" class="cta-button primary">
                <span>Get Your Free Quote</span>
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <path d="M5 12h14M12 5l7 7-7 7"/>
                </svg>
            </a>
            <a href="#products" class="cta-button secondary">
                <span>View Products</span>
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"/>
                    <circle cx="12" cy="12" r="3"/>
                </svg>
            </a>
        </div>
        <div class="hero-stats">
            <div class="stat">
                <span class="stat-number">30</span>
                <span class="stat-label">Years Experience</span>
            </div>
            <div class="stat">
                <span class="stat-number">10</span>
                <span class="stat-label">Cost of Traditional Pools</span>
            </div>
            <div class="stat">
                <span class="stat-number">1</span>
                <span class="stat-label">Assembly Time (Hours)</span>
            </div>
        </div>
    </div>
    <div class="wave-bottom"></div>
</section>

<!-- Features Section -->
<section class="features">
    <div class="container">
        <h2>Why Choose EZ Pools?</h2>
        <div class="features-grid">
            <div class="feature-card">
                <div class="feature-icon">🏊‍♂️</div>
                <h3>Made-to-Order</h3>
                <p>Every EZ Pool is custom made to your exact specifications. Any size, any color, any depth to fit your needs perfectly.</p>
            </div>
            <div class="feature-card">
                <div class="feature-icon">⚡</div>
                <h3>Quick Assembly</h3>
                <p>From carton to completion in less than one hour. No construction crews, no permits, no hassle.</p>
            </div>
            <div class="feature-card">
                <div class="feature-icon">💰</div>
                <h3>Affordable</h3>
                <p>Get all the features of traditional pools for usually 1/10th the cost. Gallon for gallon, the best value.</p>
            </div>
            <div class="feature-card">
                <div class="feature-icon">🌱</div>
                <h3>Eco-Friendly</h3>
                <p>Our eco-safe design is kind to the environment with organic heating and maintenance options.</p>
            </div>
            <div class="feature-card">
                <div class="feature-icon">🛡️</div>
                <h3>Durable</h3>
                <p>Made from quality American components. So durable they can be hit with a sledgehammer or freeze solid.</p>
            </div>
            <div class="feature-card">
                <div class="feature-icon">🔧</div>
                <h3>Versatile</h3>
                <p>Can be on-ground, in-ground, or partially buried. Perfect for any situation where traditional pools won't work.</p>
            </div>
        </div>
    </div>
</section>

<!-- Products Section -->
<section id="products" class="products">
    <div class="container">
        <h2>Our Pool Categories</h2>
        <div class="products-grid">
            <?php
            // Get pool products
            $pool_products = new WP_Query(array(
                'post_type' => 'pool_product',
                'posts_per_page' => 6,
                'post_status' => 'publish'
            ));
            
            if ($pool_products->have_posts()) :
                while ($pool_products->have_posts()) : $pool_products->the_post();
                    $width = get_post_meta(get_the_ID(), '_pool_width', true);
                    $length_min = get_post_meta(get_the_ID(), '_pool_length_min', true);
                    $length_max = get_post_meta(get_the_ID(), '_pool_length_max', true);
                    $price_range = get_post_meta(get_the_ID(), '_pool_price_range', true);
            ?>
                <div class="product-card">
                    <?php if (has_post_thumbnail()) : ?>
                        <div class="product-image">
                            <?php the_post_thumbnail('medium'); ?>
                        </div>
                    <?php else : ?>
                        <div class="product-image placeholder">
                            <?php the_title(); ?>
                        </div>
                    <?php endif; ?>
                    <div class="product-content">
                        <h3><?php the_title(); ?></h3>
                        <?php if ($width && $length_min) : ?>
                            <div class="product-specs">
                                <span class="spec">
                                    <?php echo $width; ?>' × <?php echo $length_min; ?>'
                                    <?php if ($length_max && $length_max != $length_min) : ?>
                                        - <?php echo $length_max; ?>'
                                    <?php endif; ?>
                                </span>
                                <?php if ($price_range) : ?>
                                    <span class="price"><?php echo $price_range; ?></span>
                                <?php endif; ?>
                            </div>
                        <?php endif; ?>
                        <p><?php echo get_the_excerpt(); ?></p>
                        <a href="<?php the_permalink(); ?>" class="product-link">Learn More →</a>
                    </div>
                </div>
            <?php 
                endwhile;
                wp_reset_postdata();
            else :
                // Fallback content if no products exist yet
            ?>
                <div class="product-card">
                    <div class="product-image">7' Wide Lap Pools</div>
                    <div class="product-content">
                        <h3>Lap Pools</h3>
                        <p>Perfect for fitness and training. Available in lengths from 12' to 47'. Ideal for personal training and therapy.</p>
                        <a href="#" class="product-link">Learn More →</a>
                    </div>
                </div>
                <div class="product-card">
                    <div class="product-image">12' Wide Family Pools</div>
                    <div class="product-content">
                        <h3>Family Pools</h3>
                        <p>Great for families and recreation. 12' wide pools from 12' to 47' long. Perfect backyard solution.</p>
                        <a href="#" class="product-link">Learn More →</a>
                    </div>
                </div>
                <div class="product-card">
                    <div class="product-image">17' Wide Super Pools</div>
                    <div class="product-content">
                        <h3>Super Pools</h3>
                        <p>Larger pools for bigger families. 17' wide options from 17' to 47' long. Maximum swimming space.</p>
                        <a href="#" class="product-link">Learn More →</a>
                    </div>
                </div>
                <div class="product-card">
                    <div class="product-image">22' Wide Giant Pools</div>
                    <div class="product-content">
                        <h3>Giant Pools</h3>
                        <p>Our largest standard pools. 22' wide from 22' to 52' long. Perfect for events and large gatherings.</p>
                        <a href="#" class="product-link">Learn More →</a>
                    </div>
                </div>
                <div class="product-card">
                    <div class="product-image">Custom Event Pools</div>
                    <div class="product-content">
                        <h3>Event Pools</h3>
                        <p>Special event pools for baptisms, competitions, TV productions, and marketing events. Any size possible.</p>
                        <a href="#" class="product-link">Learn More →</a>
                    </div>
                </div>
                <div class="product-card">
                    <div class="product-image">Dog & Canine Pools</div>
                    <div class="product-content">
                        <h3>Dog Pools</h3>
                        <p>Specialized pools for canine training, therapy, and recreation. Durable and safe for our four-legged friends.</p>
                        <a href="#" class="product-link">Learn More →</a>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>
</section>

<!-- Testimonials Section -->
<section class="testimonials">
    <div class="container">
        <h2>What Our Customers Say</h2>
        <div class="testimonials-slider">
            <?php
            $testimonials = new WP_Query(array(
                'post_type' => 'testimonial',
                'posts_per_page' => 3,
                'post_status' => 'publish'
            ));
            
            if ($testimonials->have_posts()) :
                $slide_count = 0;
                while ($testimonials->have_posts()) : $testimonials->the_post();
                    $active_class = ($slide_count === 0) ? 'active' : '';
            ?>
                <div class="testimonial-slide <?php echo $active_class; ?>">
                    <div class="testimonial-content">
                        <?php the_content(); ?>
                    </div>
                </div>
            <?php 
                    $slide_count++;
                endwhile;
                wp_reset_postdata();
            else :
                // Fallback testimonials
            ?>
                <div class="testimonial-slide active">
                    <div class="testimonial-content">
                        <p>"We were skeptical about a portable pool, but EZ Pools exceeded all our expectations. The assembly was incredibly easy - my husband and I had it up and running in 45 minutes. Our kids love it, and we use it every day for exercise. Best investment we've made for our backyard!"</p>
                        <strong>- Sarah Johnson, Florida</strong>
                    </div>
                </div>
                <div class="testimonial-slide">
                    <div class="testimonial-content">
                        <p>"As a swim coach, I needed a pool that could be set up quickly for our training camp. EZ Pools delivered exactly what we needed. The 25-meter pool was perfect for our Olympic hopefuls, and the quality is outstanding."</p>
                        <strong>- Coach Mike Rodriguez, California</strong>
                    </div>
                </div>
                <div class="testimonial-slide">
                    <div class="testimonial-content">
                        <p>"We got quotes for traditional in-ground pools that were over $50,000. Our EZ Pool cost a fraction of that and gives us everything we wanted. The kids are swimming every day, and we couldn't be happier."</p>
                        <strong>- The Martinez Family, Texas</strong>
                    </div>
                </div>
            <?php endif; ?>
            
            <div class="testimonial-navigation">
                <button class="testimonial-prev">‹</button>
                <div class="testimonial-dots">
                    <span class="testimonial-dot active"></span>
                    <span class="testimonial-dot"></span>
                    <span class="testimonial-dot"></span>
                </div>
                <button class="testimonial-next">›</button>
            </div>
        </div>
    </div>
</section>

<!-- About Section -->
<section id="about" class="about">
    <div class="container">
        <div class="about-content">
            <div>
                <h2>30 Years of Innovation</h2>
                <p>Thirty years ago, the term "portable pool" was an oxymoron. How can a swimming pool be portable? But families across the globe quickly realized that a portable pool means a full-size swimming pool that can be assembled by the average family in less than one hour.</p>
                <p>We've made some dramatic improvements over the years. Today we offer you all the features and benefits of a traditional in-ground pool without the hassles and expense. Customers generally stare at their fully assembled portable pool and marvel at just how easy it was to assemble.</p>
                <p>Made from quality American components, your EZ Pool will be there for many years to come. We're the whole reason why you're even considering a portable pool.</p>
                <a href="/about" class="cta-button">Learn More About Us</a>
            </div>
            <div class="about-image">
                EZ Pool Assembly Process
            </div>
        </div>
    </div>
</section>

<!-- Contact Section -->
<section id="contact" class="contact">
    <div class="container">
        <h2>Ready to Get Started?</h2>
        <p>Contact us today for your free quote and consultation. We'll help you find the perfect pool for your needs.</p>
        <div class="contact-info">
            <div class="contact-item">
                <h3>📞 Call Us</h3>
                <p><?php echo get_theme_mod('contact_phone', '(855) 4EZ-POOL'); ?></p>
            </div>
            <div class="contact-item">
                <h3>✉️ Email Us</h3>
                <p><?php echo get_theme_mod('contact_email', '<EMAIL>'); ?></p>
            </div>
            <div class="contact-item">
                <h3>🇺🇸 Made in USA</h3>
                <p>Quality American Components</p>
            </div>
        </div>
    </div>
</section>

<?php get_footer(); ?>
