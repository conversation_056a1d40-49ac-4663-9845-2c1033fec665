EZ POOLS WORDPRESS - WINDOWS DOCKER SETUP
==========================================

QUICK START (3 Steps):
1. Install Docker Desktop for Windows
2. Open PowerShell in this folder
3. Run: .\docker-start.ps1

🎉 FULLY AUTOMATED SETUP!
WordPress will be automatically installed and configured with:
- EZ Pools theme activated
- Sample pool products
- Customer testimonials
- Navigation menu
- Contact information
- Admin account ready to use

DETAILED INSTRUCTIONS:

PREREQUISITES:
- Windows 11 (or Windows 10 Pro/Enterprise)
- Docker Desktop for Windows
- PowerShell (built into Windows)

STEP 1: INSTALL DOCKER DESKTOP
1. Download from: https://docs.docker.com/desktop/windows/
2. Run the installer
3. Restart your computer when prompted
4. Start Docker Desktop from Start Menu
5. Wait for Docker to fully start (whale icon in system tray)

STEP 2: START THE WEBSITE
Option A - PowerShell (Recommended):
1. Right-click in this folder and select "Open in Terminal" or "Open PowerShell window here"
2. Run: .\docker-start.ps1
3. Wait for setup to complete
4. Website will open automatically in your browser

Option B - Command Prompt:
1. Right-click in this folder and select "Open Command Prompt here"
2. Double-click start.bat OR run: start.bat
3. Wait for setup to complete

Option C - Double-click:
1. Double-click the "start.bat" file
2. Wait for setup to complete

ACCESSING YOUR WEBSITE:
- Website: http://localhost:8080
- WordPress Admin: http://localhost:8080/wp-admin
  - Username: admin
  - Password: admin123
- Database Admin: http://localhost:8081

MANAGING THE WEBSITE:

Start the website:
  .\docker-start.ps1

Stop the website:
  .\docker-stop.ps1

View logs:
  .\docker-logs.ps1

Clean everything (removes all data):
  .\docker-clean.ps1

TROUBLESHOOTING:

"Docker not found" error:
- Make sure Docker Desktop is installed and running
- Restart PowerShell/Command Prompt after installing Docker

"Permission denied" error:
- Run PowerShell as Administrator
- Or enable execution policy: Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser

Website not loading:
- Wait a bit longer (can take 1-2 minutes first time)
- Check if containers are running: docker-compose ps
- View logs: .\docker-logs.ps1

Port already in use:
- Stop other web servers (IIS, XAMPP, etc.)
- Or change ports in docker-compose.yml

CUSTOMIZING:
- Edit theme files in: wp-content/themes/ezpools/
- Changes are automatically reflected in the running website
- WordPress admin: http://localhost:8080/wp-admin

BACKING UP:
- WordPress files: Already on your computer in wp-content/
- Database: Use phpMyAdmin at http://localhost:8081

STOPPING:
- Run: .\docker-stop.ps1
- Or: docker-compose down

COMPLETE REMOVAL:
- Run: .\docker-clean.ps1
- This removes all data and containers

SUPPORT:
- Check Docker Desktop is running (whale icon in system tray)
- View logs with: .\docker-logs.ps1
- Restart Docker Desktop if needed
- For WordPress help: http://localhost:8080/wp-admin
