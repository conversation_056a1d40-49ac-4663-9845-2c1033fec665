# EZ Pools WordPress Docker Clean Script for Windows
# This script completely removes all containers, volumes, and data

Write-Host "🧹 EZ Pools WordPress Docker Clean Script" -ForegroundColor Red
Write-Host "==========================================" -ForegroundColor Red
Write-Host ""
Write-Host "⚠️  WARNING: This will completely remove:" -ForegroundColor Yellow
Write-Host "   - All WordPress containers" -ForegroundColor White
Write-Host "   - All database data" -ForegroundColor White
Write-Host "   - All uploaded files" -ForegroundColor White
Write-Host "   - All WordPress settings" -ForegroundColor White
Write-Host ""

$confirmation = Read-Host "Are you sure you want to continue? Type 'YES' to confirm"

if ($confirmation -ne "YES") {
    Write-Host "❌ Operation cancelled." -ForegroundColor Yellow
    exit 0
}

Write-Host ""
Write-Host "🗑️  Removing containers and volumes..." -ForegroundColor Red

try {
    # Stop and remove containers
    docker-compose down -v --remove-orphans
    
    # Remove any dangling volumes
    $volumes = docker volume ls -q --filter "name=ezpools"
    if ($volumes) {
        Write-Host "🗑️  Removing EZ Pools volumes..." -ForegroundColor Yellow
        docker volume rm $volumes 2>$null
    }
    
    # Clean up any dangling images
    $images = docker images -q --filter "dangling=true"
    if ($images) {
        Write-Host "🗑️  Removing dangling images..." -ForegroundColor Yellow
        docker rmi $images 2>$null
    }
    
    Write-Host ""
    Write-Host "✅ Clean up completed successfully!" -ForegroundColor Green
    Write-Host ""
    Write-Host "📋 To start fresh:" -ForegroundColor Cyan
    Write-Host "   .\docker-start.ps1" -ForegroundColor White
    
} catch {
    Write-Host "❌ Error during cleanup: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "   You may need to manually remove containers/volumes" -ForegroundColor Yellow
}
