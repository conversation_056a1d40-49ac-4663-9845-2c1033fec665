<!DOCTYPE html><html lang="en-US"><head><title>EZ Pool Security Pool Step Enclosure Arrangement</title><link rel="shortcut icon" href="/favicon.ico" /><meta http-equiv="Content-type" content="text/html;charset=UTF-8" /><meta name="viewport" content="width=300" /><meta name="description" content="The EZ Pool Security Pool Step Enclosure Arrangement is the ultimate in portable pool step stability. Similar to the steps in your house, includes handrail, self-locking gate and platform. Free-standing and perfect for an EZ Portable Pool. Call 855-439-7665." /><meta property="og:description" content="The EZ Pool Security Pool Step Enclosure Arrangement is the ultimate in portable pool step stability. Similar to the steps in your house, includes handrail, self-locking gate and platform. Free-standing and perfect for an EZ Portable Pool. Call 855-439-7665." /><meta name="generator" content="EverWeb 4.0.1 (2884)" /><meta name="buildDate" content="Thursday, June 19, 2025" /><meta property="og:url" content="https://4ezpool.com/step-system.html" /><meta property="og:title" content="EZ Pool Security Pool Step Enclosure Arrangement" /><meta property="og:type" content="website" /><link rel="stylesheet" type="text/css" href="ew_css/textstyles.css?3833225815" /><link rel="stylesheet" type="text/css" href="ew_css/responsive.css?3833225815" /><link rel="stylesheet" type="text/css" href="ew_css/globaltextstyles.css?3833225815" /><script type="text/javascript" src="ew_js/imageCode.js" defer></script><script language="JavaScript">

<!--

function MM_swapImgRestore() { //v3.0

  var i,x,a=document.MM_sr; for(i=0;a&&i<a.length&&(x=a[i])&&x.oSrc;i++) x.src=x.oSrc;

}



function MM_preloadImages() { //v3.0

  var d=document; if(d.images){ if(!d.MM_p) d.MM_p=new Array();

    var i,j=d.MM_p.length,a=MM_preloadImages.arguments; for(i=0; i<a.length; i++)

    if (a[i].indexOf("#")!=0){ d.MM_p[j]=new Image; d.MM_p[j++].src=a[i];}}

}



function MM_findObj(n, d) { //v4.0

  var p,i,x;  if(!d) d=document; if((p=n.indexOf("?"))>0&&parent.frames.length) {

    d=parent.frames[n.substring(p+1)].document; n=n.substring(0,p);}

  if(!(x=d[n])&&d.all) x=d.all[n]; for (i=0;!x&&i<d.forms.length;i++) x=d.forms[i][n];

  for(i=0;!x&&d.layers&&i<d.layers.length;i++) x=MM_findObj(n,d.layers[i].document);

  if(!x && document.getElementById) x=document.getElementById(n); return x;

}



function MM_swapImage() { //v3.0

  var i,j=0,x,a=MM_swapImage.arguments; document.MM_sr=new Array; for(i=0;i<(a.length-2);i+=3)

   if ((x=MM_findObj(a[i]))!=null){document.MM_sr[j++]=x; if(!x.oSrc) x.oSrc=x.src; x.src=a[i+2];}

}


////////////////////////////////////////////////////////////////

    // make new window function

    ////////////////////////////////////////////////////////////////

    function createNewWin(newURL)

    {

        var newWindow;



        newWindow = window.open(newURL,"","scrollbars=1,resizable=1,height=400,width=400");

        

    }

	//  -->

  </script><style type="text/css">a img {border:0px;}body {background-color: #FFFEFE;margin: 0px auto;}div.container {margin: 0px auto;width: 300px;height: 584px;}</style></head><body><div class="container" style="height:584px"><div class="content" data-minheight="300"><div style="position:relative"><div class="shape_0" style="left:24px;top:13px;width:251px;height:114px;z-index:0;position: absolute;"><div class="paraWrap" style="padding: 17px 2.16px 0px 2.16px; "><p class="para27"><span style="line-height: 19.8px;" class="Style220">EZ Pool Steps w/ Self-Closing Gate</span></p><p class="para52"><span style="line-height: 15.4px;" class="Style229">Holds upto 300lb</span></p><p class="para52"><span style="line-height: 13.2px;" class="Style219">when properly mounted</span></p></div></div></div><div style="position:relative"><div class="shape_1" style="left:0px;top:118px;width:289px;height:294px;z-index:1;position: absolute;"><img src="images/step-system/step-inset-new.jpg" height="294" width="289" alt="EZ Pool Step System" /></div></div><div style="position:relative"><div class="shape_2" style="left:0px;top:421px;width:288px;height:163px;z-index:2;position: absolute;"><p class="para4"><span style="line-height: 21.74584px;" class="Style5">The EZ Pool Security Pool Step Enclosure Arrangement is the ultimate in portable pool step stability. Similar to the steps in your house, includes handrail, self-locking gate and platform. Free-standing and perfect for an EZ Portable Pool.</span></p></div></div></div><footer data-top='584' data-height='0'></footer></div></body></html>