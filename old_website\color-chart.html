<!DOCTYPE html><html lang="en-US"><head><title>EZ Pools can be almost any color</title><link rel="shortcut icon" href="/favicon.ico" /><meta http-equiv="Content-type" content="text/html;charset=UTF-8" /><meta name="viewport" content="width=300" /><meta name="description" content="Your Made-to-Order EZ Portable Pool can be any color, even a combination of colors. Whatever your color needs are, EZ Pools can do it. Call 855-439-7665." /><meta property="og:description" content="Your Made-to-Order EZ Portable Pool can be any color, even a combination of colors. Whatever your color needs are, EZ Pools can do it. Call 855-439-7665." /><meta name="generator" content="EverWeb 4.0.1 (2884)" /><meta name="buildDate" content="Thursday, June 19, 2025" /><meta property="og:url" content="https://4ezpool.com/color-chart.html" /><meta property="og:title" content="EZ Pools can be almost any color" /><meta property="og:type" content="website" /><link rel="stylesheet" type="text/css" href="ew_css/textstyles.css?3833225815" /><link rel="stylesheet" type="text/css" href="ew_css/responsive.css?3833225815" /><link rel="stylesheet" type="text/css" href="ew_css/globaltextstyles.css?3833225815" /><script type="text/javascript" src="ew_js/imageCode.js" defer></script><script language="JavaScript">

<!--

function MM_swapImgRestore() { //v3.0

  var i,x,a=document.MM_sr; for(i=0;a&&i<a.length&&(x=a[i])&&x.oSrc;i++) x.src=x.oSrc;

}



function MM_preloadImages() { //v3.0

  var d=document; if(d.images){ if(!d.MM_p) d.MM_p=new Array();

    var i,j=d.MM_p.length,a=MM_preloadImages.arguments; for(i=0; i<a.length; i++)

    if (a[i].indexOf("#")!=0){ d.MM_p[j]=new Image; d.MM_p[j++].src=a[i];}}

}



function MM_findObj(n, d) { //v4.0

  var p,i,x;  if(!d) d=document; if((p=n.indexOf("?"))>0&&parent.frames.length) {

    d=parent.frames[n.substring(p+1)].document; n=n.substring(0,p);}

  if(!(x=d[n])&&d.all) x=d.all[n]; for (i=0;!x&&i<d.forms.length;i++) x=d.forms[i][n];

  for(i=0;!x&&d.layers&&i<d.layers.length;i++) x=MM_findObj(n,d.layers[i].document);

  if(!x && document.getElementById) x=document.getElementById(n); return x;

}



function MM_swapImage() { //v3.0

  var i,j=0,x,a=MM_swapImage.arguments; document.MM_sr=new Array; for(i=0;i<(a.length-2);i+=3)

   if ((x=MM_findObj(a[i]))!=null){document.MM_sr[j++]=x; if(!x.oSrc) x.oSrc=x.src; x.src=a[i+2];}

}


////////////////////////////////////////////////////////////////

    // make new window function

    ////////////////////////////////////////////////////////////////

    function createNewWin(newURL)

    {

        var newWindow;



        newWindow = window.open(newURL,"","scrollbars=1,resizable=1,height=400,width=400");

        

    }

	//  -->

  </script><style type="text/css">a img {border:0px;}body {background-color: #FFFEFE;margin: 0px auto;}div.container {margin: 0px auto;width: 300px;height: 4009px;}</style></head><body><div class="container" style="height:4009px"><div class="content" data-minheight="300"><div style="position:relative"><div class="shape_0" style="left:0px;top:42px;width:303px;height:253px;z-index:0;position: absolute;"><img src="images/color-chart/ColorChoice.png" height="253" width="303" /></div></div><div style="position:relative"><div class="shape_1" style="left:7px;top:3949px;width:245px;height:60px;z-index:1;position: absolute;"><img src="images/color-chart/Logo.png" height="60" width="245" data-src2x="images/color-chart/<EMAIL>" srcset="images/color-chart/Logo.png 1x, images/color-chart/<EMAIL> 2x" /></div></div><div style="position:relative"><div class="shape_2" style="left:16px;top:320px;width:273px;height:97px;z-index:2;position: absolute;"><p class="para4"><span style="line-height: 21.74584px;" class="Style5">EZ Pools are unlike any other portable pool in that they are made-to-order and can therefore be any color. Come on! Make the pool You want.</span></p></div></div></div><footer data-top='4009' data-height='0'></footer></div></body></html>