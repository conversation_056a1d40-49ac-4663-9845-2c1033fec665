<?php
/**
 * <PERSON><PERSON><PERSON> to activate the EZ Pools theme
 */

// Include WordPress
require_once('wp-config.php');
require_once('wp-load.php');

// Activate our custom theme
$theme = 'ezpools';

// Check if theme exists
if (wp_get_theme($theme)->exists()) {
    // Switch to our theme
    switch_theme($theme);
    echo "✅ Successfully activated the '$theme' theme!\n";
    
    // Set front page to static page if it exists
    $front_page = get_page_by_path('home');
    if ($front_page) {
        update_option('show_on_front', 'page');
        update_option('page_on_front', $front_page->ID);
        echo "✅ Set front page to Home\n";
    }
    
    // Clear any caches
    if (function_exists('wp_cache_flush')) {
        wp_cache_flush();
        echo "✅ Cache cleared\n";
    }
    
    echo "\n🎉 Theme activation complete! Visit http://localhost:8080 to see the changes.\n";
} else {
    echo "❌ Theme '$theme' not found!\n";
    echo "Available themes:\n";
    $themes = wp_get_themes();
    foreach ($themes as $theme_name => $theme_obj) {
        echo "- $theme_name\n";
    }
}
?>
