<?php
/**
 * <PERSON><PERSON>t to clear WordPress cache and force refresh
 */

// Include WordPress
require_once('wp-config.php');
require_once('wp-load.php');

// Clear WordPress cache
if (function_exists('wp_cache_flush')) {
    wp_cache_flush();
    echo "✅ WordPress cache cleared\n";
}

// Clear any object cache
if (function_exists('wp_cache_delete')) {
    wp_cache_delete('alloptions', 'options');
    echo "✅ Options cache cleared\n";
}

// Force theme refresh by updating theme mod
update_option('_theme_cache_bust', time());
echo "✅ Theme cache busted\n";

// Clear any transients
delete_transient('ezpools_cache');
echo "✅ Transients cleared\n";

echo "\n🎉 All caches cleared! Please refresh your browser (Ctrl+F5 or Cmd+Shift+R)\n";
?>
